-- PostgreSQL initialization script
-- This script runs when the database is first created

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance (these will be created by Alembic migrations)
-- This file is mainly for any initial setup that needs to happen before the application starts

-- Set timezone
SET timezone = 'America/Argentina/Buenos_Aires';

-- Log the initialization
DO $$
BEGIN
    RAISE NOTICE 'Database initialized successfully for e-commerce scraper';
END $$;
