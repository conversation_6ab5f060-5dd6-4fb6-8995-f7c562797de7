"""
CRUD operations for database models.
"""
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from .models import ScrapingSite, ScrapingJob, Product, ProductPriceHistory, JobStatus, SiteStatus


class ScrapingSiteCRUD:
    """CRUD operations for ScrapingSite model."""
    
    @staticmethod
    def create(db: Session, name: str, base_url: str, spider_name: str, **kwargs) -> ScrapingSite:
        """Create a new scraping site."""
        site = ScrapingSite(
            name=name,
            base_url=base_url,
            spider_name=spider_name,
            **kwargs
        )
        db.add(site)
        db.commit()
        db.refresh(site)
        return site
    
    @staticmethod
    def get_by_id(db: Session, site_id: int) -> Optional[ScrapingSite]:
        """Get site by ID."""
        return db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
    
    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100) -> List[ScrapingSite]:
        """Get all sites with pagination."""
        return db.query(ScrapingSite).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_active(db: Session) -> List[ScrapingSite]:
        """Get all active sites."""
        return db.query(ScrapingSite).filter(
            and_(
                ScrapingSite.status == SiteStatus.ACTIVE,
                ScrapingSite.enabled == True
            )
        ).all()
    
    @staticmethod
    def update(db: Session, site_id: int, **kwargs) -> Optional[ScrapingSite]:
        """Update site."""
        site = db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
        if site:
            for key, value in kwargs.items():
                setattr(site, key, value)
            site.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(site)
        return site
    
    @staticmethod
    def delete(db: Session, site_id: int) -> bool:
        """Delete site."""
        site = db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
        if site:
            db.delete(site)
            db.commit()
            return True
        return False


class ScrapingJobCRUD:
    """CRUD operations for ScrapingJob model."""
    
    @staticmethod
    def create(db: Session, site_id: int, **kwargs) -> ScrapingJob:
        """Create a new scraping job."""
        job = ScrapingJob(site_id=site_id, **kwargs)
        db.add(job)
        db.commit()
        db.refresh(job)
        return job
    
    @staticmethod
    def get_by_id(db: Session, job_id: int) -> Optional[ScrapingJob]:
        """Get job by ID."""
        return db.query(ScrapingJob).filter(ScrapingJob.id == job_id).first()
    
    @staticmethod
    def get_by_celery_task_id(db: Session, task_id: str) -> Optional[ScrapingJob]:
        """Get job by Celery task ID."""
        return db.query(ScrapingJob).filter(ScrapingJob.celery_task_id == task_id).first()
    
    @staticmethod
    def get_recent(db: Session, limit: int = 50) -> List[ScrapingJob]:
        """Get recent jobs."""
        return db.query(ScrapingJob).order_by(desc(ScrapingJob.created_at)).limit(limit).all()
    
    @staticmethod
    def get_by_site(db: Session, site_id: int, limit: int = 20) -> List[ScrapingJob]:
        """Get jobs for a specific site."""
        return db.query(ScrapingJob).filter(
            ScrapingJob.site_id == site_id
        ).order_by(desc(ScrapingJob.created_at)).limit(limit).all()
    
    @staticmethod
    def update_status(db: Session, job_id: int, status: JobStatus, **kwargs) -> Optional[ScrapingJob]:
        """Update job status."""
        job = db.query(ScrapingJob).filter(ScrapingJob.id == job_id).first()
        if job:
            job.status = status
            job.updated_at = datetime.utcnow()
            
            if status == JobStatus.RUNNING and not job.started_at:
                job.started_at = datetime.utcnow()
            elif status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                if not job.completed_at:
                    job.completed_at = datetime.utcnow()
                if job.started_at:
                    job.duration_seconds = (job.completed_at - job.started_at).total_seconds()
            
            for key, value in kwargs.items():
                setattr(job, key, value)
            
            db.commit()
            db.refresh(job)
        return job


class ProductCRUD:
    """CRUD operations for Product model."""
    
    @staticmethod
    def create_or_update(db: Session, site_id: int, external_id: str, **kwargs) -> Product:
        """Create or update a product."""
        product = db.query(Product).filter(
            and_(
                Product.site_id == site_id,
                Product.external_id == external_id
            )
        ).first()
        
        if product:
            # Update existing product
            old_price = product.price
            for key, value in kwargs.items():
                setattr(product, key, value)
            product.last_updated_at = datetime.utcnow()
            product.scraped_at = datetime.utcnow()
            
            # Track price changes
            if old_price != product.price and product.price is not None:
                ProductPriceHistory.create_price_record(db, product.id, product.price, product.original_price)
        else:
            # Create new product
            product = Product(
                site_id=site_id,
                external_id=external_id,
                **kwargs
            )
            db.add(product)
            db.flush()  # Get the ID
            
            # Create initial price record
            if product.price is not None:
                ProductPriceHistory.create_price_record(db, product.id, product.price, product.original_price)
        
        db.commit()
        db.refresh(product)
        return product
    
    @staticmethod
    def get_by_site(db: Session, site_id: int, skip: int = 0, limit: int = 100) -> List[Product]:
        """Get products for a specific site."""
        return db.query(Product).filter(
            and_(
                Product.site_id == site_id,
                Product.is_active == True
            )
        ).offset(skip).limit(limit).all()
    
    @staticmethod
    def search(db: Session, query: str, site_id: Optional[int] = None, 
               category: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[Product]:
        """Search products."""
        filters = [Product.is_active == True]
        
        if query:
            filters.append(
                or_(
                    Product.name.ilike(f"%{query}%"),
                    Product.description.ilike(f"%{query}%")
                )
            )
        
        if site_id:
            filters.append(Product.site_id == site_id)
        
        if category:
            filters.append(Product.category.ilike(f"%{category}%"))
        
        return db.query(Product).filter(and_(*filters)).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_price_changes(db: Session, days: int = 7) -> List[Product]:
        """Get products with recent price changes."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return db.query(Product).join(ProductPriceHistory).filter(
            ProductPriceHistory.recorded_at >= cutoff_date
        ).distinct().all()


class ProductPriceHistoryCRUD:
    """CRUD operations for ProductPriceHistory model."""
    
    @staticmethod
    def create_price_record(db: Session, product_id: int, price: float, 
                          original_price: Optional[float] = None, **kwargs) -> ProductPriceHistory:
        """Create a price history record."""
        record = ProductPriceHistory(
            product_id=product_id,
            price=price,
            original_price=original_price,
            **kwargs
        )
        db.add(record)
        db.commit()
        db.refresh(record)
        return record
    
    @staticmethod
    def get_product_history(db: Session, product_id: int, days: int = 30) -> List[ProductPriceHistory]:
        """Get price history for a product."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return db.query(ProductPriceHistory).filter(
            and_(
                ProductPriceHistory.product_id == product_id,
                ProductPriceHistory.recorded_at >= cutoff_date
            )
        ).order_by(ProductPriceHistory.recorded_at).all()


# Convenience functions
def get_dashboard_stats(db: Session) -> Dict[str, Any]:
    """Get dashboard statistics."""
    total_sites = db.query(ScrapingSite).count()
    active_sites = db.query(ScrapingSite).filter(ScrapingSite.status == SiteStatus.ACTIVE).count()
    total_products = db.query(Product).filter(Product.is_active == True).count()
    
    # Recent jobs
    recent_jobs = db.query(ScrapingJob).order_by(desc(ScrapingJob.created_at)).limit(5).all()
    
    # Jobs in last 24 hours
    yesterday = datetime.utcnow() - timedelta(days=1)
    jobs_24h = db.query(ScrapingJob).filter(ScrapingJob.created_at >= yesterday).count()
    
    return {
        "total_sites": total_sites,
        "active_sites": active_sites,
        "total_products": total_products,
        "recent_jobs": recent_jobs,
        "jobs_24h": jobs_24h
    }
