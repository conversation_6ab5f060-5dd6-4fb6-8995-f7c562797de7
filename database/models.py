"""
Database models for the e-commerce scraping application.
"""
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, 
    Float, JSON, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class JobStatus(str, Enum):
    """Job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SiteStatus(str, Enum):
    """Site status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"


class ScrapingSite(Base):
    """Model for scraping target sites."""
    __tablename__ = "scraping_sites"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    base_url = Column(String(500), nullable=False)
    spider_name = Column(String(100), nullable=False)
    status = Column(String(20), default=SiteStatus.ACTIVE)
    
    # Configuration
    config = Column(JSON, default={})  # Spider-specific configuration
    
    # Scheduling
    enabled = Column(Boolean, default=True)
    schedule_hour = Column(Integer, default=10)
    schedule_minute = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_scraped_at = Column(DateTime, nullable=True)
    
    # Relationships
    products = relationship("Product", back_populates="site", cascade="all, delete-orphan")
    jobs = relationship("ScrapingJob", back_populates="site", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ScrapingSite(name='{self.name}', url='{self.base_url}')>"


class ScrapingJob(Base):
    """Model for scraping job tracking."""
    __tablename__ = "scraping_jobs"

    id = Column(Integer, primary_key=True, index=True)
    site_id = Column(Integer, ForeignKey("scraping_sites.id"), nullable=False)
    
    # Job details
    status = Column(String(20), default=JobStatus.PENDING)
    celery_task_id = Column(String(255), nullable=True, index=True)
    
    # Timing
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    
    # Results
    items_scraped = Column(Integer, default=0)
    items_saved = Column(Integer, default=0)
    errors_count = Column(Integer, default=0)
    
    # Logs and metadata
    log_messages = Column(JSON, default=[])
    error_messages = Column(JSON, default=[])
    job_metadata = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    site = relationship("ScrapingSite", back_populates="jobs")

    def __repr__(self):
        return f"<ScrapingJob(id={self.id}, site_id={self.site_id}, status='{self.status}')>"


class Product(Base):
    """Model for scraped product data."""
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    site_id = Column(Integer, ForeignKey("scraping_sites.id"), nullable=False)
    
    # Product identifiers
    sku = Column(String(255), nullable=True, index=True)
    external_id = Column(String(255), nullable=True, index=True)
    url = Column(String(1000), nullable=False)
    
    # Basic product information
    name = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(255), nullable=True, index=True)
    brand = Column(String(255), nullable=True, index=True)
    
    # Pricing
    price = Column(Float, nullable=True)
    original_price = Column(Float, nullable=True)
    currency = Column(String(10), default="ARS")
    discount_percentage = Column(Float, nullable=True)
    
    # Availability
    in_stock = Column(Boolean, nullable=True)
    stock_quantity = Column(Integer, nullable=True)
    availability_text = Column(String(255), nullable=True)
    
    # Images and media
    image_urls = Column(JSON, default=[])
    main_image_url = Column(String(1000), nullable=True)
    
    # Additional data
    attributes = Column(JSON, default={})  # Flexible storage for site-specific data
    rating = Column(Float, nullable=True)
    review_count = Column(Integer, nullable=True)
    
    # Data tracking
    first_seen_at = Column(DateTime, default=func.now())
    last_updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    scraped_at = Column(DateTime, default=func.now())
    
    # Data integrity
    is_active = Column(Boolean, default=True)
    hash_signature = Column(String(64), nullable=True, index=True)  # For deduplication
    
    # Relationships
    site = relationship("ScrapingSite", back_populates="products")
    price_history = relationship("ProductPriceHistory", back_populates="product", cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index('idx_product_site_sku', 'site_id', 'sku'),
        Index('idx_product_site_external_id', 'site_id', 'external_id'),
        Index('idx_product_category_brand', 'category', 'brand'),
        Index('idx_product_price_range', 'price'),
        Index('idx_product_scraped_at', 'scraped_at'),
        UniqueConstraint('site_id', 'external_id', name='uq_site_external_id'),
    )

    def __repr__(self):
        return f"<Product(id={self.id}, name='{self.name[:50]}...', price={self.price})>"


class ProductPriceHistory(Base):
    """Model for tracking product price changes over time."""
    __tablename__ = "product_price_history"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Price data
    price = Column(Float, nullable=False)
    original_price = Column(Float, nullable=True)
    currency = Column(String(10), default="ARS")
    discount_percentage = Column(Float, nullable=True)
    
    # Availability at time of recording
    in_stock = Column(Boolean, nullable=True)
    stock_quantity = Column(Integer, nullable=True)
    
    # Timestamp
    recorded_at = Column(DateTime, default=func.now(), index=True)
    
    # Relationships
    product = relationship("Product", back_populates="price_history")

    # Indexes
    __table_args__ = (
        Index('idx_price_history_product_date', 'product_id', 'recorded_at'),
    )

    def __repr__(self):
        return f"<ProductPriceHistory(product_id={self.product_id}, price={self.price}, recorded_at={self.recorded_at})>"
