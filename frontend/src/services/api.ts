// API service for communicating with the backend

import axios from 'axios';
import { Site, Product, Job, DashboardStats, ProductFilter, PaginationParams } from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Sites API
export const sitesApi = {
  getAll: () => api.get<Site[]>('/api/sites/'),
  getActive: () => api.get<Site[]>('/api/sites/active'),
  getById: (id: number) => api.get<Site>(`/api/sites/${id}`),
  create: (site: Omit<Site, 'id' | 'created_at' | 'updated_at' | 'last_scraped_at'>) =>
    api.post<Site>('/api/sites/', site),
  update: (id: number, site: Partial<Site>) => api.put<Site>(`/api/sites/${id}`, site),
  delete: (id: number) => api.delete(`/api/sites/${id}`),
};

// Products API
export const productsApi = {
  getAll: (params?: ProductFilter & PaginationParams) =>
    api.get<Product[]>('/api/products/', { params }),
  search: (query: string, params?: { site_id?: number; category?: string } & PaginationParams) =>
    api.get<Product[]>('/api/products/search', { params: { q: query, ...params } }),
  getById: (id: number) => api.get<Product>(`/api/products/${id}`),
  getBySite: (siteId: number, params?: PaginationParams) =>
    api.get<Product[]>(`/api/products/site/${siteId}`, { params }),
  getCategories: (siteId?: number) =>
    api.get<string[]>('/api/products/categories', { params: { site_id: siteId } }),
  getBrands: (siteId?: number, category?: string) =>
    api.get<string[]>('/api/products/brands', { params: { site_id: siteId, category } }),
  getPriceChanges: (days: number) =>
    api.get<Product[]>(`/api/products/price-changes/${days}`),
  delete: (id: number) => api.delete(`/api/products/${id}`),
};

// Jobs API
export const jobsApi = {
  getAll: (params?: { site_id?: number } & PaginationParams) =>
    api.get<Job[]>('/api/jobs/', { params }),
  getRecent: (limit?: number) =>
    api.get<Job[]>('/api/jobs/recent', { params: { limit } }),
  getById: (id: number) => api.get<Job>(`/api/jobs/${id}`),
  getBySite: (siteId: number, limit?: number) =>
    api.get<Job[]>(`/api/jobs/site/${siteId}`, { params: { limit } }),
  create: (siteId: number) => api.post<Job>('/api/jobs/', { site_id: siteId }),
  triggerScraping: (siteId: number) => api.post<Job>(`/api/jobs/site/${siteId}`),
  cancel: (id: number) => api.delete(`/api/jobs/${id}`),
};

// Dashboard API
export const dashboardApi = {
  getStats: () => api.get<DashboardStats>('/api/dashboard/stats'),
  getMetrics: () => api.get('/api/dashboard/metrics'),
  getHealth: () => api.get('/api/dashboard/health'),
};

// Export API
export const exportApi = {
  exportProductsCSV: (params?: {
    site_id?: number;
    category?: string;
    date_from?: string;
    date_to?: string;
    include_inactive?: boolean;
  }) => {
    return api.get('/api/export/csv', {
      params,
      responseType: 'blob',
    });
  },
  exportProductsJSON: (params?: {
    site_id?: number;
    category?: string;
    date_from?: string;
    date_to?: string;
    include_inactive?: boolean;
    limit?: number;
  }) => {
    return api.get('/api/export/json', {
      params,
      responseType: 'blob',
    });
  },
  exportSitesCSV: () => {
    return api.get('/api/export/sites/csv', {
      responseType: 'blob',
    });
  },
};

// Health check
export const healthApi = {
  check: () => api.get('/health'),
};

export default api;
