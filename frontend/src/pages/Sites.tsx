import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

import { sitesApi, jobsApi } from '../services/api';
import { Site } from '../types';

const Sites: React.FC = () => {
  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingSite, setEditingSite] = useState<Site | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    base_url: '',
    spider_name: '',
    enabled: true,
    schedule_hour: 10,
    schedule_minute: 0,
  });

  useEffect(() => {
    fetchSites();
  }, []);

  const fetchSites = async () => {
    try {
      setLoading(true);
      const response = await sitesApi.getAll();
      setSites(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch sites');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (site?: Site) => {
    if (site) {
      setEditingSite(site);
      setFormData({
        name: site.name,
        base_url: site.base_url,
        spider_name: site.spider_name,
        enabled: site.enabled,
        schedule_hour: site.schedule_hour,
        schedule_minute: site.schedule_minute,
      });
    } else {
      setEditingSite(null);
      setFormData({
        name: '',
        base_url: '',
        spider_name: '',
        enabled: true,
        schedule_hour: 10,
        schedule_minute: 0,
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingSite(null);
  };

  const handleSave = async () => {
    try {
      if (editingSite) {
        await sitesApi.update(editingSite.id, formData);
      } else {
        await sitesApi.create(formData as any);
      }
      handleCloseDialog();
      fetchSites();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save site');
    }
  };

  const handleDelete = async (site: Site) => {
    if (window.confirm(`Are you sure you want to delete "${site.name}"?`)) {
      try {
        await sitesApi.delete(site.id);
        fetchSites();
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to delete site');
      }
    }
  };

  const handleTriggerScraping = async (site: Site) => {
    try {
      await jobsApi.triggerScraping(site.id);
      alert(`Scraping job started for ${site.name}`);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to trigger scraping');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Sites</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add Site
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>URL</TableCell>
                  <TableCell>Spider</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Enabled</TableCell>
                  <TableCell>Schedule</TableCell>
                  <TableCell>Last Scraped</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sites.map((site) => (
                  <TableRow key={site.id}>
                    <TableCell>{site.name}</TableCell>
                    <TableCell>
                      <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                        {site.base_url}
                      </Typography>
                    </TableCell>
                    <TableCell>{site.spider_name}</TableCell>
                    <TableCell>
                      <Chip
                        label={site.status}
                        color={getStatusColor(site.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={site.enabled ? 'Yes' : 'No'}
                        color={site.enabled ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {String(site.schedule_hour).padStart(2, '0')}:
                      {String(site.schedule_minute).padStart(2, '0')}
                    </TableCell>
                    <TableCell>
                      {site.last_scraped_at
                        ? format(new Date(site.last_scraped_at), 'MMM dd, HH:mm')
                        : 'Never'}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleTriggerScraping(site)}
                        disabled={!site.enabled}
                        title="Trigger scraping"
                      >
                        <PlayIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(site)}
                        title="Edit"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(site)}
                        title="Delete"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingSite ? 'Edit Site' : 'Add New Site'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Base URL"
              value={formData.base_url}
              onChange={(e) => setFormData({ ...formData, base_url: e.target.value })}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Spider Name"
              value={formData.spider_name}
              onChange={(e) => setFormData({ ...formData, spider_name: e.target.value })}
              margin="normal"
              required
            />
            <Box display="flex" gap={2} mt={2}>
              <TextField
                label="Schedule Hour"
                type="number"
                value={formData.schedule_hour}
                onChange={(e) => setFormData({ ...formData, schedule_hour: parseInt(e.target.value) })}
                inputProps={{ min: 0, max: 23 }}
                sx={{ flex: 1 }}
              />
              <TextField
                label="Schedule Minute"
                type="number"
                value={formData.schedule_minute}
                onChange={(e) => setFormData({ ...formData, schedule_minute: parseInt(e.target.value) })}
                inputProps={{ min: 0, max: 59 }}
                sx={{ flex: 1 }}
              />
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.enabled}
                  onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
                />
              }
              label="Enabled"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingSite ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Sites;
