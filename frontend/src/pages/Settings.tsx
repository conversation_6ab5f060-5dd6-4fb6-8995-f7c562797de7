import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  CheckCircle as HealthyIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

import { dashboardApi, exportApi } from '../services/api';

interface HealthStatus {
  status: string;
  database: string;
  celery: string;
}

interface SystemMetrics {
  timestamp: string;
  jobs: {
    last_24h: number;
    last_7d: number;
    last_30d: number;
    success_rate_7d: number;
    avg_duration_seconds?: number;
  };
  products: {
    total_active: number;
    scraped_24h: number;
    scraped_7d: number;
  };
  sites: {
    total: number;
    active: number;
    enabled: number;
  };
}

const Settings: React.FC = () => {
  const [health, setHealth] = useState<HealthStatus | null>(null);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    fetchSystemInfo();
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemInfo, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemInfo = async () => {
    try {
      setLoading(true);
      const [healthResponse, metricsResponse] = await Promise.all([
        dashboardApi.getHealth(),
        dashboardApi.getMetrics(),
      ]);
      setHealth(healthResponse.data);
      setMetrics(metricsResponse.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch system information');
    } finally {
      setLoading(false);
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <HealthyIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'error':
      case 'unhealthy':
        return <ErrorIcon color="error" />;
      default:
        return <WarningIcon color="disabled" />;
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
      case 'unhealthy':
        return 'error';
      default:
        return 'default';
    }
  };

  const handleExportAll = async () => {
    try {
      setExportLoading(true);
      await exportApi.exportProductsCSV({});
      setExportDialogOpen(false);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Export failed');
    } finally {
      setExportLoading(false);
    }
  };

  if (loading && !health && !metrics) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Settings & System Status</Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchSystemInfo}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* System Health */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Health
              </Typography>
              {health ? (
                <Box>
                  <Box display="flex" alignItems="center" gap={1} mb={2}>
                    {getHealthIcon(health.status)}
                    <Chip
                      label={health.status.toUpperCase()}
                      color={getHealthColor(health.status) as any}
                    />
                  </Box>
                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography>Database:</Typography>
                      <Chip
                        label={health.database}
                        color={getHealthColor(health.database) as any}
                        size="small"
                      />
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography>Celery:</Typography>
                      <Chip
                        label={health.celery}
                        color={getHealthColor(health.celery) as any}
                        size="small"
                      />
                    </Box>
                  </Box>
                </Box>
              ) : (
                <Typography color="textSecondary">Loading...</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* System Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Metrics
              </Typography>
              {metrics ? (
                <Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Last updated: {new Date(metrics.timestamp).toLocaleString()}
                  </Typography>
                  <Box mt={2}>
                    <Typography variant="subtitle2" gutterBottom>Jobs</Typography>
                    <Typography variant="body2">Last 24h: {metrics.jobs.last_24h}</Typography>
                    <Typography variant="body2">Last 7d: {metrics.jobs.last_7d}</Typography>
                    <Typography variant="body2">Success rate (7d): {metrics.jobs.success_rate_7d}%</Typography>
                    {metrics.jobs.avg_duration_seconds && (
                      <Typography variant="body2">
                        Avg duration: {Math.round(metrics.jobs.avg_duration_seconds)}s
                      </Typography>
                    )}
                  </Box>
                </Box>
              ) : (
                <Typography color="textSecondary">Loading...</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Product Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Product Statistics
              </Typography>
              {metrics ? (
                <Box>
                  <Typography variant="body2">
                    Total active: {metrics.products.total_active.toLocaleString()}
                  </Typography>
                  <Typography variant="body2">
                    Scraped (24h): {metrics.products.scraped_24h.toLocaleString()}
                  </Typography>
                  <Typography variant="body2">
                    Scraped (7d): {metrics.products.scraped_7d.toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Typography color="textSecondary">Loading...</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Site Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Site Statistics
              </Typography>
              {metrics ? (
                <Box>
                  <Typography variant="body2">
                    Total sites: {metrics.sites.total}
                  </Typography>
                  <Typography variant="body2">
                    Active sites: {metrics.sites.active}
                  </Typography>
                  <Typography variant="body2">
                    Enabled sites: {metrics.sites.enabled}
                  </Typography>
                </Box>
              ) : (
                <Typography color="textSecondary">Loading...</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Data Management */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Data Management
              </Typography>
              <Box display="flex" gap={2} flexWrap="wrap">
                <Button
                  variant="outlined"
                  onClick={() => setExportDialogOpen(true)}
                >
                  Export All Data
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => exportApi.exportSitesCSV()}
                >
                  Export Sites Config
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Configuration */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Configuration
              </Typography>
              <Typography variant="body2" color="textSecondary">
                System configuration is managed through environment variables.
                Key settings include:
              </Typography>
              <Box mt={2}>
                <Typography variant="body2">• Database connection</Typography>
                <Typography variant="body2">• Redis connection for job queue</Typography>
                <Typography variant="body2">• Scraping schedules and delays</Typography>
                <Typography variant="body2">• Data retention policies</Typography>
                <Typography variant="body2">• Email notifications</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)}>
        <DialogTitle>Export All Data</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            This will export all active products to a CSV file. Large datasets may take some time to process.
          </Typography>
          <Typography variant="body2" color="textSecondary">
            The export will include all products from all sites with their current information.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleExportAll}
            variant="contained"
            disabled={exportLoading}
          >
            {exportLoading ? <CircularProgress size={20} /> : 'Export'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Settings;
