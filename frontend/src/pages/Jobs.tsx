import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  LinearProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Stop as StopIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

import { jobsApi } from '../services/api';
import { Job } from '../types';

const Jobs: React.FC = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  useEffect(() => {
    fetchJobs();
    // Set up auto-refresh for running jobs
    const interval = setInterval(() => {
      fetchJobs();
    }, 10000); // Refresh every 10 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await jobsApi.getAll({ skip: 0, limit: 100 });
      setJobs(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelJob = async (job: Job) => {
    if (window.confirm(`Are you sure you want to cancel the job for ${job.site_name}?`)) {
      try {
        await jobsApi.cancel(job.id);
        fetchJobs();
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to cancel job');
      }
    }
  };

  const handleViewDetails = (job: Job) => {
    setSelectedJob(job);
    setDetailsOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'info';
      case 'failed':
        return 'error';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getSuccessRate = (job: Job) => {
    if (job.items_scraped === 0) return 0;
    return Math.round((job.items_saved / job.items_scraped) * 100);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Jobs</Typography>
        <IconButton onClick={fetchJobs} title="Refresh">
          <RefreshIcon />
        </IconButton>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Site</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Progress</TableCell>
                    <TableCell>Items</TableCell>
                    <TableCell>Errors</TableCell>
                    <TableCell>Duration</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {jobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell>{job.site_name || 'Unknown'}</TableCell>
                      <TableCell>
                        <Chip
                          label={job.status}
                          color={getStatusColor(job.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {job.status === 'running' ? (
                          <Box sx={{ width: 100 }}>
                            <LinearProgress />
                            <Typography variant="caption" color="textSecondary">
                              Running...
                            </Typography>
                          </Box>
                        ) : job.status === 'completed' ? (
                          <Box sx={{ width: 100 }}>
                            <LinearProgress variant="determinate" value={100} color="success" />
                            <Typography variant="caption" color="textSecondary">
                              {getSuccessRate(job)}% success
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="caption" color="textSecondary">
                            {job.status}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            Scraped: {job.items_scraped}
                          </Typography>
                          <Typography variant="body2">
                            Saved: {job.items_saved}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {job.errors_count > 0 ? (
                          <Chip
                            label={job.errors_count}
                            color="error"
                            size="small"
                          />
                        ) : (
                          '0'
                        )}
                      </TableCell>
                      <TableCell>
                        {formatDuration(job.duration_seconds)}
                      </TableCell>
                      <TableCell>
                        <Tooltip title={format(new Date(job.created_at), 'PPpp')}>
                          <span>
                            {format(new Date(job.created_at), 'MMM dd, HH:mm')}
                          </span>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(job)}
                          title="View details"
                        >
                          <ViewIcon />
                        </IconButton>
                        {(job.status === 'running' || job.status === 'pending') && (
                          <IconButton
                            size="small"
                            onClick={() => handleCancelJob(job)}
                            title="Cancel job"
                          >
                            <StopIcon />
                          </IconButton>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Job Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Job Details - {selectedJob?.site_name}
        </DialogTitle>
        <DialogContent>
          {selectedJob && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Box mb={2}>
                <Typography><strong>Status:</strong> {selectedJob.status}</Typography>
                <Typography><strong>Created:</strong> {format(new Date(selectedJob.created_at), 'PPpp')}</Typography>
                {selectedJob.started_at && (
                  <Typography><strong>Started:</strong> {format(new Date(selectedJob.started_at), 'PPpp')}</Typography>
                )}
                {selectedJob.completed_at && (
                  <Typography><strong>Completed:</strong> {format(new Date(selectedJob.completed_at), 'PPpp')}</Typography>
                )}
                <Typography><strong>Duration:</strong> {formatDuration(selectedJob.duration_seconds)}</Typography>
              </Box>

              <Typography variant="h6" gutterBottom>
                Results
              </Typography>
              <Box mb={2}>
                <Typography><strong>Items Scraped:</strong> {selectedJob.items_scraped}</Typography>
                <Typography><strong>Items Saved:</strong> {selectedJob.items_saved}</Typography>
                <Typography><strong>Errors:</strong> {selectedJob.errors_count}</Typography>
                <Typography><strong>Success Rate:</strong> {getSuccessRate(selectedJob)}%</Typography>
              </Box>

              {selectedJob.error_messages.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom color="error">
                    Error Messages
                  </Typography>
                  <Box mb={2}>
                    {selectedJob.error_messages.slice(0, 5).map((error, index) => (
                      <Typography key={index} variant="body2" color="error" sx={{ mb: 1 }}>
                        {error}
                      </Typography>
                    ))}
                    {selectedJob.error_messages.length > 5 && (
                      <Typography variant="caption" color="textSecondary">
                        ... and {selectedJob.error_messages.length - 5} more errors
                      </Typography>
                    )}
                  </Box>
                </>
              )}

              {selectedJob.log_messages.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Log Messages (Last 10)
                  </Typography>
                  <Box
                    sx={{
                      maxHeight: 200,
                      overflow: 'auto',
                      backgroundColor: '#f5f5f5',
                      p: 1,
                      borderRadius: 1,
                    }}
                  >
                    {selectedJob.log_messages.slice(-10).map((log, index) => (
                      <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                        {log}
                      </Typography>
                    ))}
                  </Box>
                </>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Jobs;
