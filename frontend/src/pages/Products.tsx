import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  Pagination,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  Search as SearchIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

import { productsApi, sitesApi, exportApi } from '../services/api';
import { Product, Site, ProductFilter } from '../types';

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [sites, setSites] = useState<Site[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [brands, setBrands] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<ProductFilter>({});
  const [searchQuery, setSearchQuery] = useState('');

  const itemsPerPage = 50;

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [page, filters]);

  const fetchInitialData = async () => {
    try {
      const [sitesResponse, categoriesResponse] = await Promise.all([
        sitesApi.getAll(),
        productsApi.getCategories(),
      ]);
      setSites(sitesResponse.data);
      setCategories(categoriesResponse.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch initial data');
    }
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        skip: (page - 1) * itemsPerPage,
        limit: itemsPerPage,
      };
      const response = await productsApi.getAll(params);
      setProducts(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      try {
        setLoading(true);
        const response = await productsApi.search(searchQuery, {
          site_id: filters.site_id,
          category: filters.category,
          skip: 0,
          limit: itemsPerPage,
        });
        setProducts(response.data);
        setPage(1);
      } catch (err: any) {
        setError(err.response?.data?.error || 'Search failed');
      } finally {
        setLoading(false);
      }
    } else {
      fetchProducts();
    }
  };

  const handleFilterChange = (field: keyof ProductFilter, value: any) => {
    const newFilters = { ...filters, [field]: value || undefined };
    setFilters(newFilters);
    setPage(1);

    // Fetch brands when category changes
    if (field === 'category' && value) {
      fetchBrands(newFilters.site_id, value);
    }
  };

  const fetchBrands = async (siteId?: number, category?: string) => {
    try {
      const response = await productsApi.getBrands(siteId, category);
      setBrands(response.data);
    } catch (err) {
      console.error('Failed to fetch brands:', err);
    }
  };

  const handleExport = async (format: 'csv' | 'json') => {
    try {
      const exportParams = {
        site_id: filters.site_id,
        category: filters.category,
        include_inactive: false,
      };

      const response = format === 'csv' 
        ? await exportApi.exportProductsCSV(exportParams)
        : await exportApi.exportProductsJSON(exportParams);

      // Create download link
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `products_export.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Export failed');
    }
  };

  const formatPrice = (price?: number, currency = 'ARS') => {
    if (price === undefined || price === null) return 'N/A';
    return new Intl.NumberFormat('es-AR', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Products</Typography>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => handleExport('csv')}
          >
            Export CSV
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => handleExport('json')}
          >
            Export JSON
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Filters
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search products"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Button onClick={handleSearch}>
                        <SearchIcon />
                      </Button>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Site</InputLabel>
                <Select
                  value={filters.site_id || ''}
                  onChange={(e) => handleFilterChange('site_id', e.target.value)}
                  label="Site"
                >
                  <MenuItem value="">All Sites</MenuItem>
                  {sites.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category || ''}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  label="Category"
                >
                  <MenuItem value="">All Categories</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Brand</InputLabel>
                <Select
                  value={filters.brand || ''}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  label="Brand"
                >
                  <MenuItem value="">All Brands</MenuItem>
                  {brands.map((brand) => (
                    <MenuItem key={brand} value={brand}>
                      {brand}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Stock Status</InputLabel>
                <Select
                  value={filters.in_stock !== undefined ? String(filters.in_stock) : ''}
                  onChange={(e) => handleFilterChange('in_stock', e.target.value === '' ? undefined : e.target.value === 'true')}
                  label="Stock Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">In Stock</MenuItem>
                  <MenuItem value="false">Out of Stock</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>Site</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Brand</TableCell>
                      <TableCell>Price</TableCell>
                      <TableCell>Stock</TableCell>
                      <TableCell>Last Updated</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {products.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Avatar
                              src={product.main_image_url}
                              alt={product.name}
                              sx={{ width: 40, height: 40 }}
                            />
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {product.name}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                SKU: {product.sku || 'N/A'}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{product.site_name || 'Unknown'}</TableCell>
                        <TableCell>{product.category || 'N/A'}</TableCell>
                        <TableCell>{product.brand || 'N/A'}</TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {formatPrice(product.price, product.currency)}
                            </Typography>
                            {product.original_price && product.original_price !== product.price && (
                              <Typography variant="caption" color="textSecondary" sx={{ textDecoration: 'line-through' }}>
                                {formatPrice(product.original_price, product.currency)}
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={product.in_stock ? 'In Stock' : 'Out of Stock'}
                            color={product.in_stock ? 'success' : 'error'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {format(new Date(product.last_updated_at), 'MMM dd, HH:mm')}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              <Box display="flex" justifyContent="center" mt={3}>
                <Pagination
                  count={Math.ceil(1000 / itemsPerPage)} // Approximate, would need total count from API
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Products;
