// Type definitions for the application

export interface Site {
  id: number;
  name: string;
  base_url: string;
  spider_name: string;
  status: 'active' | 'inactive' | 'error';
  enabled: boolean;
  schedule_hour: number;
  schedule_minute: number;
  config: Record<string, any>;
  created_at: string;
  updated_at: string;
  last_scraped_at?: string;
}

export interface Product {
  id: number;
  site_id: number;
  site_name?: string;
  name: string;
  description?: string;
  category?: string;
  brand?: string;
  price?: number;
  original_price?: number;
  currency: string;
  in_stock?: boolean;
  stock_quantity?: number;
  availability_text?: string;
  sku?: string;
  external_id: string;
  url: string;
  image_urls: string[];
  main_image_url?: string;
  attributes: Record<string, any>;
  rating?: number;
  review_count?: number;
  first_seen_at: string;
  last_updated_at: string;
  scraped_at: string;
  is_active: boolean;
}

export interface Job {
  id: number;
  site_id: number;
  site_name?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  celery_task_id?: string;
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  items_scraped: number;
  items_saved: number;
  errors_count: number;
  log_messages: string[];
  error_messages: string[];
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface DashboardStats {
  total_sites: number;
  active_sites: number;
  total_products: number;
  jobs_24h: number;
  recent_jobs: Job[];
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  skip: number;
  limit: number;
}

export interface ProductFilter {
  site_id?: number;
  category?: string;
  brand?: string;
  min_price?: number;
  max_price?: number;
  in_stock?: boolean;
  search?: string;
}

export interface ExportRequest {
  format: 'csv' | 'json';
  site_id?: number;
  category?: string;
  date_from?: string;
  date_to?: string;
  include_inactive?: boolean;
}
