"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TestCache = void 0;
var _cache = require("../../hooks/features/dataSource/cache");
class TestCache {
  constructor() {
    this.cache = void 0;
    this.cache = new Map();
  }
  set(key, value) {
    this.cache.set((0, _cache.getKeyDefault)(key), value);
  }
  get(key) {
    return this.cache.get((0, _cache.getKeyDefault)(key));
  }
  size() {
    return this.cache.size;
  }
  clear() {
    this.cache.clear();
  }
}
exports.TestCache = TestCache;