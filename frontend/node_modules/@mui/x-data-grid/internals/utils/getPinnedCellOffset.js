"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getPinnedCellOffset = void 0;
var _constants = require("../constants");
const getPinnedCellOffset = (pinnedPosition, computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth) => {
  let pinnedOffset;
  switch (pinnedPosition) {
    case _constants.PinnedColumnPosition.LEFT:
      pinnedOffset = columnPositions[columnIndex];
      break;
    case _constants.PinnedColumnPosition.RIGHT:
      pinnedOffset = columnsTotalWidth - columnPositions[columnIndex] - computedWidth + scrollbarWidth;
      break;
    default:
      pinnedOffset = undefined;
      break;
  }

  // XXX: fix this properly
  if (Number.isNaN(pinnedOffset)) {
    pinnedOffset = undefined;
  }
  return pinnedOffset;
};
exports.getPinnedCellOffset = getPinnedCellOffset;