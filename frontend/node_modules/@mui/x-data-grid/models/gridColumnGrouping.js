"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isLeaf = isLeaf;
function isLeaf(node) {
  return node.field !== undefined;
}

/**
 * A function used to process headerClassName params.
 * @param {GridColumnGroupHeaderParams} params The parameters of the column group header.
 * @returns {string} The class name to be added to the column group header cell.
 */

/**
 * The union type representing the [[GridColDef]] column header class type.
 */