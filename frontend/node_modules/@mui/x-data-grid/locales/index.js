"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _arSD = require("./arSD");
Object.keys(_arSD).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _arSD[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _arSD[key];
    }
  });
});
var _beBY = require("./beBY");
Object.keys(_beBY).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _beBY[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _beBY[key];
    }
  });
});
var _bgBG = require("./bgBG");
Object.keys(_bgBG).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _bgBG[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _bgBG[key];
    }
  });
});
var _bnBD = require("./bnBD");
Object.keys(_bnBD).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _bnBD[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _bnBD[key];
    }
  });
});
var _csCZ = require("./csCZ");
Object.keys(_csCZ).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _csCZ[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _csCZ[key];
    }
  });
});
var _daDK = require("./daDK");
Object.keys(_daDK).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _daDK[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _daDK[key];
    }
  });
});
var _deDE = require("./deDE");
Object.keys(_deDE).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _deDE[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _deDE[key];
    }
  });
});
var _elGR = require("./elGR");
Object.keys(_elGR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _elGR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _elGR[key];
    }
  });
});
var _enUS = require("./enUS");
Object.keys(_enUS).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _enUS[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _enUS[key];
    }
  });
});
var _esES = require("./esES");
Object.keys(_esES).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _esES[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _esES[key];
    }
  });
});
var _faIR = require("./faIR");
Object.keys(_faIR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _faIR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _faIR[key];
    }
  });
});
var _fiFI = require("./fiFI");
Object.keys(_fiFI).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _fiFI[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _fiFI[key];
    }
  });
});
var _frFR = require("./frFR");
Object.keys(_frFR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _frFR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _frFR[key];
    }
  });
});
var _heIL = require("./heIL");
Object.keys(_heIL).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _heIL[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _heIL[key];
    }
  });
});
var _huHU = require("./huHU");
Object.keys(_huHU).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _huHU[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _huHU[key];
    }
  });
});
var _hyAM = require("./hyAM");
Object.keys(_hyAM).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _hyAM[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _hyAM[key];
    }
  });
});
var _itIT = require("./itIT");
Object.keys(_itIT).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _itIT[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _itIT[key];
    }
  });
});
var _jaJP = require("./jaJP");
Object.keys(_jaJP).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _jaJP[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _jaJP[key];
    }
  });
});
var _koKR = require("./koKR");
Object.keys(_koKR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _koKR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _koKR[key];
    }
  });
});
var _nbNO = require("./nbNO");
Object.keys(_nbNO).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _nbNO[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _nbNO[key];
    }
  });
});
var _nlNL = require("./nlNL");
Object.keys(_nlNL).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _nlNL[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _nlNL[key];
    }
  });
});
var _nnNO = require("./nnNO");
Object.keys(_nnNO).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _nnNO[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _nnNO[key];
    }
  });
});
var _plPL = require("./plPL");
Object.keys(_plPL).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _plPL[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _plPL[key];
    }
  });
});
var _ptBR = require("./ptBR");
Object.keys(_ptBR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _ptBR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _ptBR[key];
    }
  });
});
var _roRO = require("./roRO");
Object.keys(_roRO).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _roRO[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _roRO[key];
    }
  });
});
var _ruRU = require("./ruRU");
Object.keys(_ruRU).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _ruRU[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _ruRU[key];
    }
  });
});
var _skSK = require("./skSK");
Object.keys(_skSK).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _skSK[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _skSK[key];
    }
  });
});
var _svSE = require("./svSE");
Object.keys(_svSE).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _svSE[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _svSE[key];
    }
  });
});
var _trTR = require("./trTR");
Object.keys(_trTR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _trTR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _trTR[key];
    }
  });
});
var _ukUA = require("./ukUA");
Object.keys(_ukUA).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _ukUA[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _ukUA[key];
    }
  });
});
var _urPK = require("./urPK");
Object.keys(_urPK).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _urPK[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _urPK[key];
    }
  });
});
var _viVN = require("./viVN");
Object.keys(_viVN).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _viVN[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _viVN[key];
    }
  });
});
var _zhCN = require("./zhCN");
Object.keys(_zhCN).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _zhCN[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _zhCN[key];
    }
  });
});
var _zhTW = require("./zhTW");
Object.keys(_zhTW).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _zhTW[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _zhTW[key];
    }
  });
});
var _hrHR = require("./hrHR");
Object.keys(_hrHR).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _hrHR[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _hrHR[key];
    }
  });
});
var _ptPT = require("./ptPT");
Object.keys(_ptPT).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _ptPT[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _ptPT[key];
    }
  });
});
var _zhHK = require("./zhHK");
Object.keys(_zhHK).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _zhHK[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _zhHK[key];
    }
  });
});
var _isIS = require("./isIS");
Object.keys(_isIS).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _isIS[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _isIS[key];
    }
  });
});
var _idID = require("./idID");
Object.keys(_idID).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _idID[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _idID[key];
    }
  });
});