"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridColumnSortButton = GridColumnSortButton;
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _system = require("@mui/system");
var _clsx = _interopRequireDefault(require("clsx"));
var _useGridApiContext = require("../hooks/utils/useGridApiContext");
var _gridClasses = require("../constants/gridClasses");
var _useGridRootProps = require("../hooks/utils/useGridRootProps");
var _cssVariables = require("../constants/cssVariables");
var _GridColumnUnsortedIcon = require("./GridColumnUnsortedIcon");
var _assert = require("../utils/assert");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["direction", "index", "sortingOrder", "disabled", "className"];
const useUtilityClasses = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['sortButton'],
    icon: ['sortIcon']
  };
  return (0, _composeClasses.default)(slots, _gridClasses.getDataGridUtilityClass, classes);
};
const GridColumnSortButtonRoot = (0, _system.styled)(_assert.NotRendered, {
  name: 'MuiDataGrid',
  slot: 'SortButton'
})({
  transition: _cssVariables.vars.transition(['opacity'], {
    duration: _cssVariables.vars.transitions.duration.short,
    easing: _cssVariables.vars.transitions.easing.easeInOut
  })
});
function getIcon(icons, direction, className, sortingOrder) {
  let Icon;
  const iconProps = {};
  if (direction === 'asc') {
    Icon = icons.columnSortedAscendingIcon;
  } else if (direction === 'desc') {
    Icon = icons.columnSortedDescendingIcon;
  } else {
    Icon = _GridColumnUnsortedIcon.GridColumnUnsortedIcon;
    iconProps.sortingOrder = sortingOrder;
  }
  return Icon ? /*#__PURE__*/(0, _jsxRuntime.jsx)(Icon, (0, _extends2.default)({
    fontSize: "small",
    className: className
  }, iconProps)) : null;
}
function GridColumnSortButton(props) {
  const {
      direction,
      index,
      sortingOrder,
      disabled,
      className
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const apiRef = (0, _useGridApiContext.useGridApiContext)();
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const ownerState = (0, _extends2.default)({}, props, {
    classes: rootProps.classes
  });
  const classes = useUtilityClasses(ownerState);
  const iconElement = getIcon(rootProps.slots, direction, classes.icon, sortingOrder);
  if (!iconElement) {
    return null;
  }
  const iconButton = /*#__PURE__*/(0, _jsxRuntime.jsx)(GridColumnSortButtonRoot, (0, _extends2.default)({
    as: rootProps.slots.baseIconButton,
    ownerState: ownerState,
    "aria-label": apiRef.current.getLocaleText('columnHeaderSortIconLabel'),
    title: apiRef.current.getLocaleText('columnHeaderSortIconLabel'),
    size: "small",
    disabled: disabled,
    className: (0, _clsx.default)(classes.root, className)
  }, rootProps.slotProps?.baseIconButton, other, {
    children: iconElement
  }));
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
    children: [index != null && /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseBadge, {
      badgeContent: index,
      color: "default",
      overlap: "circular",
      children: iconButton
    }), index == null && iconButton]
  });
}
process.env.NODE_ENV !== "production" ? GridColumnSortButton.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  direction: _propTypes.default.oneOf(['asc', 'desc']),
  disabled: _propTypes.default.bool,
  field: _propTypes.default.string.isRequired,
  index: _propTypes.default.number,
  onClick: _propTypes.default.func,
  sortingOrder: _propTypes.default.arrayOf(_propTypes.default.oneOf(['asc', 'desc'])).isRequired
} : void 0;