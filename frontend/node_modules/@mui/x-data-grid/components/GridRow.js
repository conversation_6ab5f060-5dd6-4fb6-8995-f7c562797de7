"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridRow = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _clsx = _interopRequireDefault(require("clsx"));
var _useForkRef = _interopRequireDefault(require("@mui/utils/useForkRef"));
var _fastMemo = require("@mui/x-internals/fastMemo");
var _forwardRef = require("@mui/x-internals/forwardRef");
var _isObjectEmpty = require("@mui/x-internals/isObjectEmpty");
var _gridEditRowModel = require("../models/gridEditRowModel");
var _gridClasses = require("../constants/gridClasses");
var _composeGridClasses = require("../utils/composeGridClasses");
var _useGridRootProps = require("../hooks/utils/useGridRootProps");
var _cellBorderUtils = require("../utils/cellBorderUtils");
var _gridColumnsSelector = require("../hooks/features/columns/gridColumnsSelector");
var _useGridSelector = require("../hooks/utils/useGridSelector");
var _useGridVisibleRows = require("../hooks/utils/useGridVisibleRows");
var _domUtils = require("../utils/domUtils");
var _gridCheckboxSelectionColDef = require("../colDef/gridCheckboxSelectionColDef");
var _gridActionsColDef = require("../colDef/gridActionsColDef");
var _constants = require("../internals/constants");
var _gridSortingSelector = require("../hooks/features/sorting/gridSortingSelector");
var _gridRowsSelector = require("../hooks/features/rows/gridRowsSelector");
var _gridEditingSelectors = require("../hooks/features/editing/gridEditingSelectors");
var _GridScrollbarFillerCell = require("./GridScrollbarFillerCell");
var _getPinnedCellOffset = require("../internals/utils/getPinnedCellOffset");
var _useGridConfiguration = require("../hooks/utils/useGridConfiguration");
var _useGridPrivateApiContext = require("../hooks/utils/useGridPrivateApiContext");
var _createSelector = require("../utils/createSelector");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["selected", "rowId", "row", "index", "style", "rowHeight", "className", "visibleColumns", "pinnedColumns", "offsetLeft", "columnsTotalWidth", "firstColumnIndex", "lastColumnIndex", "focusedColumnIndex", "isFirstVisible", "isLastVisible", "isNotVisible", "showBottomBorder", "scrollbarWidth", "gridHasFiller", "onClick", "onDoubleClick", "onMouseEnter", "onMouseLeave", "onMouseOut", "onMouseOver"];
const isRowReorderingEnabledSelector = (0, _createSelector.createSelector)(_gridEditingSelectors.gridEditRowsStateSelector, (editRows, rowReordering) => {
  if (!rowReordering) {
    return false;
  }
  const isEditingRows = !(0, _isObjectEmpty.isObjectEmpty)(editRows);
  return !isEditingRows;
});
const GridRow = (0, _forwardRef.forwardRef)(function GridRow(props, refProp) {
  const {
      selected,
      rowId,
      row,
      index,
      style: styleProp,
      rowHeight,
      className,
      visibleColumns,
      pinnedColumns,
      offsetLeft,
      columnsTotalWidth,
      firstColumnIndex,
      lastColumnIndex,
      focusedColumnIndex,
      isFirstVisible,
      isLastVisible,
      isNotVisible,
      showBottomBorder,
      scrollbarWidth,
      gridHasFiller,
      onClick,
      onDoubleClick,
      onMouseEnter,
      onMouseLeave,
      onMouseOut,
      onMouseOver
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const apiRef = (0, _useGridPrivateApiContext.useGridPrivateApiContext)();
  const configuration = (0, _useGridConfiguration.useGridConfiguration)();
  const ref = React.useRef(null);
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const currentPage = (0, _useGridVisibleRows.useGridVisibleRows)(apiRef, rootProps);
  const sortModel = (0, _useGridSelector.useGridSelector)(apiRef, _gridSortingSelector.gridSortModelSelector);
  const treeDepth = (0, _useGridSelector.useGridSelector)(apiRef, _gridRowsSelector.gridRowMaximumTreeDepthSelector);
  const columnPositions = (0, _useGridSelector.useGridSelector)(apiRef, _gridColumnsSelector.gridColumnPositionsSelector);
  const rowReordering = rootProps.rowReordering;
  const isRowReorderingEnabled = (0, _useGridSelector.useGridSelector)(apiRef, isRowReorderingEnabledSelector, rowReordering);
  const handleRef = (0, _useForkRef.default)(ref, refProp);
  const rowNode = (0, _gridRowsSelector.gridRowNodeSelector)(apiRef, rowId);
  const editing = (0, _useGridSelector.useGridSelector)(apiRef, _gridEditingSelectors.gridRowIsEditingSelector, {
    rowId,
    editMode: rootProps.editMode
  });
  const editable = rootProps.editMode === _gridEditRowModel.GridEditModes.Row;
  const hasFocusCell = focusedColumnIndex !== undefined;
  const hasVirtualFocusCellLeft = hasFocusCell && focusedColumnIndex >= pinnedColumns.left.length && focusedColumnIndex < firstColumnIndex;
  const hasVirtualFocusCellRight = hasFocusCell && focusedColumnIndex < visibleColumns.length - pinnedColumns.right.length && focusedColumnIndex >= lastColumnIndex;
  const classes = (0, _composeGridClasses.composeGridClasses)(rootProps.classes, {
    root: ['row', selected && 'selected', editable && 'row--editable', editing && 'row--editing', isFirstVisible && 'row--firstVisible', isLastVisible && 'row--lastVisible', showBottomBorder && 'row--borderBottom', rowHeight === 'auto' && 'row--dynamicHeight']
  });
  const getRowAriaAttributes = configuration.hooks.useGridRowAriaAttributes();
  React.useLayoutEffect(() => {
    if (currentPage.range) {
      const rowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(rowId);
      // Pinned rows are not part of the visible rows
      if (rowIndex !== undefined) {
        apiRef.current.unstable_setLastMeasuredRowIndex(rowIndex);
      }
    }
    if (ref.current && rowHeight === 'auto') {
      return apiRef.current.observeRowHeight(ref.current, rowId);
    }
    return undefined;
  }, [apiRef, currentPage.range, rowHeight, rowId]);
  const publish = React.useCallback((eventName, propHandler) => event => {
    // Ignore portal
    if ((0, _domUtils.isEventTargetInPortal)(event)) {
      return;
    }

    // The row might have been deleted
    if (!apiRef.current.getRow(rowId)) {
      return;
    }
    apiRef.current.publishEvent(eventName, apiRef.current.getRowParams(rowId), event);
    if (propHandler) {
      propHandler(event);
    }
  }, [apiRef, rowId]);
  const publishClick = React.useCallback(event => {
    const cell = (0, _domUtils.findParentElementFromClassName)(event.target, _gridClasses.gridClasses.cell);
    const field = cell?.getAttribute('data-field');

    // Check if the field is available because the cell that fills the empty
    // space of the row has no field.
    if (field) {
      // User clicked in the checkbox added by checkboxSelection
      if (field === _gridCheckboxSelectionColDef.GRID_CHECKBOX_SELECTION_COL_DEF.field) {
        return;
      }

      // User opened a detail panel
      if (field === _constants.GRID_DETAIL_PANEL_TOGGLE_FIELD) {
        return;
      }

      // User reorders a row
      if (field === '__reorder__') {
        return;
      }

      // User is editing a cell
      if (apiRef.current.getCellMode(rowId, field) === _gridEditRowModel.GridCellModes.Edit) {
        return;
      }

      // User clicked a button from the "actions" column type
      const column = apiRef.current.getColumn(field);
      if (column?.type === _gridActionsColDef.GRID_ACTIONS_COLUMN_TYPE) {
        return;
      }
    }
    publish('rowClick', onClick)(event);
  }, [apiRef, onClick, publish, rowId]);
  const {
    slots,
    slotProps,
    disableColumnReorder
  } = rootProps;
  const heightEntry = (0, _useGridSelector.useGridSelector)(apiRef, () => (0, _extends2.default)({}, apiRef.current.getRowHeightEntry(rowId)), undefined, _useGridSelector.objectShallowCompare);
  const style = React.useMemo(() => {
    if (isNotVisible) {
      return {
        opacity: 0,
        width: 0,
        height: 0
      };
    }
    const rowStyle = (0, _extends2.default)({}, styleProp, {
      maxHeight: rowHeight === 'auto' ? 'none' : rowHeight,
      // max-height doesn't support "auto"
      minHeight: rowHeight,
      '--height': typeof rowHeight === 'number' ? `${rowHeight}px` : rowHeight
    });
    if (heightEntry.spacingTop) {
      const property = rootProps.rowSpacingType === 'border' ? 'borderTopWidth' : 'marginTop';
      rowStyle[property] = heightEntry.spacingTop;
    }
    if (heightEntry.spacingBottom) {
      const property = rootProps.rowSpacingType === 'border' ? 'borderBottomWidth' : 'marginBottom';
      let propertyValue = rowStyle[property];
      // avoid overriding existing value
      if (typeof propertyValue !== 'number') {
        propertyValue = parseInt(propertyValue || '0', 10);
      }
      propertyValue += heightEntry.spacingBottom;
      rowStyle[property] = propertyValue;
    }
    return rowStyle;
  }, [isNotVisible, rowHeight, styleProp, heightEntry, rootProps.rowSpacingType]);
  const rowClassNames = apiRef.current.unstable_applyPipeProcessors('rowClassName', [], rowId);
  const ariaAttributes = getRowAriaAttributes(rowNode, index);
  if (typeof rootProps.getRowClassName === 'function') {
    const indexRelativeToCurrentPage = index - (currentPage.range?.firstRowIndex || 0);
    const rowParams = (0, _extends2.default)({}, apiRef.current.getRowParams(rowId), {
      isFirstVisible: indexRelativeToCurrentPage === 0,
      isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,
      indexRelativeToCurrentPage
    });
    rowClassNames.push(rootProps.getRowClassName(rowParams));
  }

  // XXX: fix this properly
  if (!rowNode) {
    return null;
  }
  const getCell = (column, indexInSection, indexRelativeToAllColumns, sectionLength, pinnedPosition = _constants.PinnedColumnPosition.NONE) => {
    const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, indexRelativeToAllColumns);
    if (cellColSpanInfo?.spannedByColSpan) {
      return null;
    }
    const width = cellColSpanInfo?.cellProps.width ?? column.computedWidth;
    const colSpan = cellColSpanInfo?.cellProps.colSpan ?? 1;
    const pinnedOffset = (0, _getPinnedCellOffset.getPinnedCellOffset)(pinnedPosition, column.computedWidth, indexRelativeToAllColumns, columnPositions, columnsTotalWidth, scrollbarWidth);
    if (rowNode.type === 'skeletonRow') {
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(slots.skeletonCell, {
        type: column.type,
        width: width,
        height: rowHeight,
        field: column.field,
        align: column.align
      }, column.field);
    }

    // when the cell is a reorder cell we are not allowing to reorder the col
    // fixes https://github.com/mui/mui-x/issues/11126
    const isReorderCell = column.field === '__reorder__';
    const canReorderColumn = !(disableColumnReorder || column.disableReorder);
    const canReorderRow = isRowReorderingEnabled && !sortModel.length && treeDepth <= 1;
    const disableDragEvents = !(canReorderColumn || isReorderCell && canReorderRow);
    const cellIsNotVisible = pinnedPosition === _constants.PinnedColumnPosition.VIRTUAL;
    const showLeftBorder = (0, _cellBorderUtils.shouldCellShowLeftBorder)(pinnedPosition, indexInSection);
    const showRightBorder = (0, _cellBorderUtils.shouldCellShowRightBorder)(pinnedPosition, indexInSection, sectionLength, rootProps.showCellVerticalBorder, gridHasFiller);
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(slots.cell, (0, _extends2.default)({
      column: column,
      width: width,
      rowId: rowId,
      align: column.align || 'left',
      colIndex: indexRelativeToAllColumns,
      colSpan: colSpan,
      disableDragEvents: disableDragEvents,
      isNotVisible: cellIsNotVisible,
      pinnedOffset: pinnedOffset,
      pinnedPosition: pinnedPosition,
      showLeftBorder: showLeftBorder,
      showRightBorder: showRightBorder,
      row: row,
      rowNode: rowNode
    }, slotProps?.cell), column.field);
  };
  if (process.env.NODE_ENV !== "production") getCell.displayName = "getCell";
  const leftCells = pinnedColumns.left.map((column, i) => {
    const indexRelativeToAllColumns = i;
    return getCell(column, i, indexRelativeToAllColumns, pinnedColumns.left.length, _constants.PinnedColumnPosition.LEFT);
  });
  const rightCells = pinnedColumns.right.map((column, i) => {
    const indexRelativeToAllColumns = visibleColumns.length - pinnedColumns.right.length + i;
    return getCell(column, i, indexRelativeToAllColumns, pinnedColumns.right.length, _constants.PinnedColumnPosition.RIGHT);
  });
  const middleColumnsLength = visibleColumns.length - pinnedColumns.left.length - pinnedColumns.right.length;
  const cells = [];
  if (hasVirtualFocusCellLeft) {
    cells.push(getCell(visibleColumns[focusedColumnIndex], focusedColumnIndex - pinnedColumns.left.length, focusedColumnIndex, middleColumnsLength, _constants.PinnedColumnPosition.VIRTUAL));
  }
  for (let i = firstColumnIndex; i < lastColumnIndex; i += 1) {
    const column = visibleColumns[i];
    const indexInSection = i - pinnedColumns.left.length;
    if (!column) {
      continue;
    }
    cells.push(getCell(column, indexInSection, i, middleColumnsLength));
  }
  if (hasVirtualFocusCellRight) {
    cells.push(getCell(visibleColumns[focusedColumnIndex], focusedColumnIndex - pinnedColumns.left.length, focusedColumnIndex, middleColumnsLength, _constants.PinnedColumnPosition.VIRTUAL));
  }
  const eventHandlers = row ? {
    onClick: publishClick,
    onDoubleClick: publish('rowDoubleClick', onDoubleClick),
    onMouseEnter: publish('rowMouseEnter', onMouseEnter),
    onMouseLeave: publish('rowMouseLeave', onMouseLeave),
    onMouseOut: publish('rowMouseOut', onMouseOut),
    onMouseOver: publish('rowMouseOver', onMouseOver)
  } : null;
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)("div", (0, _extends2.default)({
    "data-id": rowId,
    "data-rowindex": index,
    role: "row",
    className: (0, _clsx.default)(...rowClassNames, classes.root, className),
    style: style
  }, ariaAttributes, eventHandlers, other, {
    ref: handleRef,
    children: [leftCells, /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      role: "presentation",
      className: _gridClasses.gridClasses.cellOffsetLeft,
      style: {
        width: offsetLeft
      }
    }), cells, /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      role: "presentation",
      className: (0, _clsx.default)(_gridClasses.gridClasses.cell, _gridClasses.gridClasses.cellEmpty)
    }), rightCells, scrollbarWidth !== 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridScrollbarFillerCell.GridScrollbarFillerCell, {
      pinnedRight: pinnedColumns.right.length > 0,
      borderTop: !isFirstVisible
    })]
  }));
});
if (process.env.NODE_ENV !== "production") GridRow.displayName = "GridRow";
process.env.NODE_ENV !== "production" ? GridRow.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  columnsTotalWidth: _propTypes.default.number.isRequired,
  firstColumnIndex: _propTypes.default.number.isRequired,
  /**
   * Determines which cell has focus.
   * If `null`, no cell in this row has focus.
   */
  focusedColumnIndex: _propTypes.default.number,
  gridHasFiller: _propTypes.default.bool.isRequired,
  /**
   * Index of the row in the whole sorted and filtered dataset.
   * If some rows above have expanded children, this index also take those children into account.
   */
  index: _propTypes.default.number.isRequired,
  isFirstVisible: _propTypes.default.bool.isRequired,
  isLastVisible: _propTypes.default.bool.isRequired,
  isNotVisible: _propTypes.default.bool.isRequired,
  lastColumnIndex: _propTypes.default.number.isRequired,
  offsetLeft: _propTypes.default.number.isRequired,
  onClick: _propTypes.default.func,
  onDoubleClick: _propTypes.default.func,
  onMouseEnter: _propTypes.default.func,
  onMouseLeave: _propTypes.default.func,
  pinnedColumns: _propTypes.default.object.isRequired,
  row: _propTypes.default.object.isRequired,
  rowHeight: _propTypes.default.oneOfType([_propTypes.default.oneOf(['auto']), _propTypes.default.number]).isRequired,
  rowId: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]).isRequired,
  scrollbarWidth: _propTypes.default.number.isRequired,
  selected: _propTypes.default.bool.isRequired,
  showBottomBorder: _propTypes.default.bool.isRequired,
  visibleColumns: _propTypes.default.arrayOf(_propTypes.default.object).isRequired
} : void 0;
const MemoizedGridRow = exports.GridRow = (0, _fastMemo.fastMemo)(GridRow);