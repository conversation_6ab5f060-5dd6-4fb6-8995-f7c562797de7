"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _GridBody = require("./GridBody");
Object.keys(_GridBody).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _GridBody[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _GridBody[key];
    }
  });
});
var _GridFooterPlaceholder = require("./GridFooterPlaceholder");
Object.keys(_GridFooterPlaceholder).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _GridFooterPlaceholder[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _GridFooterPlaceholder[key];
    }
  });
});