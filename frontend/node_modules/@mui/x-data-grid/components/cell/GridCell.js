"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.gridPinnedColumnPositionLookup = exports.GridCell = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _clsx = _interopRequireDefault(require("clsx"));
var _useForkRef = _interopRequireDefault(require("@mui/utils/useForkRef"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _ownerDocument = _interopRequireDefault(require("@mui/utils/ownerDocument"));
var _capitalize = _interopRequireDefault(require("@mui/utils/capitalize"));
var _fastMemo = require("@mui/x-internals/fastMemo");
var _RtlProvider = require("@mui/system/RtlProvider");
var _forwardRef = require("@mui/x-internals/forwardRef");
var _store = require("@mui/x-internals/store");
var _xVirtualizer = require("@mui/x-virtualizer");
var _doesSupportPreventScroll = require("../../utils/doesSupportPreventScroll");
var _gridClasses = require("../../constants/gridClasses");
var _models = require("../../models");
var _useGridSelector = require("../../hooks/utils/useGridSelector");
var _useGridRootProps = require("../../hooks/utils/useGridRootProps");
var _gridFocusStateSelector = require("../../hooks/features/focus/gridFocusStateSelector");
var _gridColumnsInterfaces = require("../../hooks/features/columns/gridColumnsInterfaces");
var _constants = require("../../internals/constants");
var _useGridPrivateApiContext = require("../../hooks/utils/useGridPrivateApiContext");
var _gridEditingSelectors = require("../../hooks/features/editing/gridEditingSelectors");
var _utils = require("../../internals/utils");
var _useGridConfiguration = require("../../hooks/utils/useGridConfiguration");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["column", "row", "rowId", "rowNode", "align", "children", "colIndex", "width", "className", "style", "colSpan", "disableDragEvents", "isNotVisible", "pinnedOffset", "pinnedPosition", "showRightBorder", "showLeftBorder", "onClick", "onDoubleClick", "onMouseDown", "onMouseUp", "onMouseOver", "onKeyDown", "onKeyUp", "onDragEnter", "onDragOver"],
  _excluded2 = ["changeReason", "unstable_updateValueOnRender"];
const gridPinnedColumnPositionLookup = exports.gridPinnedColumnPositionLookup = {
  [_constants.PinnedColumnPosition.LEFT]: _gridColumnsInterfaces.GridPinnedColumnPosition.LEFT,
  [_constants.PinnedColumnPosition.RIGHT]: _gridColumnsInterfaces.GridPinnedColumnPosition.RIGHT,
  [_constants.PinnedColumnPosition.NONE]: undefined,
  [_constants.PinnedColumnPosition.VIRTUAL]: undefined
};
const useUtilityClasses = ownerState => {
  const {
    align,
    showLeftBorder,
    showRightBorder,
    pinnedPosition,
    isEditable,
    isSelected,
    isSelectionMode,
    classes
  } = ownerState;
  const slots = {
    root: ['cell', `cell--text${(0, _capitalize.default)(align)}`, isSelected && 'selected', isEditable && 'cell--editable', showLeftBorder && 'cell--withLeftBorder', showRightBorder && 'cell--withRightBorder', pinnedPosition === _constants.PinnedColumnPosition.LEFT && 'cell--pinnedLeft', pinnedPosition === _constants.PinnedColumnPosition.RIGHT && 'cell--pinnedRight', isSelectionMode && !isEditable && 'cell--selectionMode']
  };
  return (0, _composeClasses.default)(slots, _gridClasses.getDataGridUtilityClass, classes);
};
let warnedOnce = false;

// TODO(v7): Removing the wrapper will break the docs performance visualization demo.

const GridCell = (0, _forwardRef.forwardRef)(function GridCell(props, ref) {
  const {
      column,
      row,
      rowId,
      rowNode,
      align,
      colIndex,
      width,
      className,
      style: styleProp,
      colSpan,
      disableDragEvents,
      isNotVisible,
      pinnedOffset,
      pinnedPosition,
      showRightBorder,
      showLeftBorder,
      onClick,
      onDoubleClick,
      onMouseDown,
      onMouseUp,
      onMouseOver,
      onKeyDown,
      onKeyUp,
      onDragEnter,
      onDragOver
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const apiRef = (0, _useGridPrivateApiContext.useGridPrivateApiContext)();
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const isRtl = (0, _RtlProvider.useRtl)();
  const field = column.field;
  const editCellState = (0, _useGridSelector.useGridSelector)(apiRef, _gridEditingSelectors.gridEditCellStateSelector, {
    rowId,
    field
  });
  const config = (0, _useGridConfiguration.useGridConfiguration)();
  const cellAggregationResult = config.hooks.useCellAggregationResult(rowId, field);
  const cellMode = editCellState ? _models.GridCellModes.Edit : _models.GridCellModes.View;
  const cellParams = apiRef.current.getCellParamsForRow(rowId, field, row, {
    colDef: column,
    cellMode,
    rowNode: rowNode,
    tabIndex: (0, _useGridSelector.useGridSelector)(apiRef, () => {
      const cellTabIndex = (0, _gridFocusStateSelector.gridTabIndexCellSelector)(apiRef);
      return cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === rowId ? 0 : -1;
    }),
    hasFocus: (0, _useGridSelector.useGridSelector)(apiRef, () => {
      const focus = (0, _gridFocusStateSelector.gridFocusCellSelector)(apiRef);
      return focus?.id === rowId && focus.field === field;
    })
  });
  cellParams.api = apiRef.current;
  if (cellAggregationResult) {
    cellParams.value = cellAggregationResult.value;
    cellParams.formattedValue = column.valueFormatter ? column.valueFormatter(cellParams.value, row, column, apiRef) : cellParams.value;
  }
  const isSelected = (0, _useGridSelector.useGridSelector)(apiRef, () => apiRef.current.unstable_applyPipeProcessors('isCellSelected', false, {
    id: rowId,
    field
  }));
  const store = apiRef.current.virtualizer.store;
  const hiddenCells = (0, _store.useStore)(store, _xVirtualizer.Rowspan.selectors.hiddenCells);
  const spannedCells = (0, _store.useStore)(store, _xVirtualizer.Rowspan.selectors.spannedCells);
  const {
    hasFocus,
    isEditable = false,
    value
  } = cellParams;
  const canManageOwnFocus = column.type === 'actions' && column.getActions?.(apiRef.current.getRowParams(rowId)).some(action => !action.props.disabled);
  const tabIndex = (cellMode === 'view' || !isEditable) && !canManageOwnFocus ? cellParams.tabIndex : -1;
  const {
    classes: rootClasses,
    getCellClassName
  } = rootProps;

  // There is a hidden grid state access in `applyPipeProcessor('cellClassName', ...)`
  const pipesClassName = (0, _useGridSelector.useGridSelector)(apiRef, () => apiRef.current.unstable_applyPipeProcessors('cellClassName', [], {
    id: rowId,
    field
  }).filter(Boolean).join(' '));
  const classNames = [pipesClassName];
  if (column.cellClassName) {
    classNames.push(typeof column.cellClassName === 'function' ? column.cellClassName(cellParams) : column.cellClassName);
  }
  if (column.display === 'flex') {
    classNames.push(_gridClasses.gridClasses['cell--flex']);
  }
  if (getCellClassName) {
    classNames.push(getCellClassName(cellParams));
  }
  const valueToRender = cellParams.formattedValue ?? value;
  const cellRef = React.useRef(null);
  const handleRef = (0, _useForkRef.default)(ref, cellRef);
  const focusElementRef = React.useRef(null);
  const isSelectionMode = rootProps.cellSelection ?? false;
  const ownerState = {
    align,
    showLeftBorder,
    showRightBorder,
    isEditable,
    classes: rootProps.classes,
    pinnedPosition,
    isSelected,
    isSelectionMode
  };
  const classes = useUtilityClasses(ownerState);
  const publishMouseUp = React.useCallback(eventName => event => {
    const params = apiRef.current.getCellParams(rowId, field || '');
    apiRef.current.publishEvent(eventName, params, event);
    if (onMouseUp) {
      onMouseUp(event);
    }
  }, [apiRef, field, onMouseUp, rowId]);
  const publishMouseDown = React.useCallback(eventName => event => {
    const params = apiRef.current.getCellParams(rowId, field || '');
    apiRef.current.publishEvent(eventName, params, event);
    if (onMouseDown) {
      onMouseDown(event);
    }
  }, [apiRef, field, onMouseDown, rowId]);
  const publish = React.useCallback((eventName, propHandler) => event => {
    // The row might have been deleted during the click
    if (!apiRef.current.getRow(rowId)) {
      return;
    }
    const params = apiRef.current.getCellParams(rowId, field || '');
    apiRef.current.publishEvent(eventName, params, event);
    if (propHandler) {
      propHandler(event);
    }
  }, [apiRef, field, rowId]);
  const isCellRowSpanned = hiddenCells[rowId]?.[colIndex] ?? false;
  const rowSpan = spannedCells[rowId]?.[colIndex] ?? 1;
  const style = React.useMemo(() => {
    if (isNotVisible) {
      return {
        padding: 0,
        opacity: 0,
        width: 0,
        height: 0,
        border: 0
      };
    }
    const cellStyle = (0, _utils.attachPinnedStyle)((0, _extends2.default)({
      '--width': `${width}px`
    }, styleProp), isRtl, pinnedPosition, pinnedOffset);
    const isLeftPinned = pinnedPosition === _constants.PinnedColumnPosition.LEFT;
    const isRightPinned = pinnedPosition === _constants.PinnedColumnPosition.RIGHT;
    if (rowSpan > 1) {
      cellStyle.height = `calc(var(--height) * ${rowSpan})`;
      cellStyle.zIndex = 10;
      if (isLeftPinned || isRightPinned) {
        cellStyle.zIndex = 40;
      }
    }
    return cellStyle;
  }, [width, isNotVisible, styleProp, pinnedOffset, pinnedPosition, isRtl, rowSpan]);
  React.useEffect(() => {
    if (!hasFocus || cellMode === _models.GridCellModes.Edit) {
      return;
    }
    const doc = (0, _ownerDocument.default)(apiRef.current.rootElementRef.current);
    if (cellRef.current && !cellRef.current.contains(doc.activeElement)) {
      const focusableElement = cellRef.current.querySelector('[tabindex="0"]');
      const elementToFocus = focusElementRef.current || focusableElement || cellRef.current;
      if ((0, _doesSupportPreventScroll.doesSupportPreventScroll)()) {
        elementToFocus.focus({
          preventScroll: true
        });
      } else {
        const scrollPosition = apiRef.current.getScrollPosition();
        elementToFocus.focus();
        apiRef.current.scroll(scrollPosition);
      }
    }
  }, [hasFocus, cellMode, apiRef]);
  if (isCellRowSpanned) {
    return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", {
      "data-colindex": colIndex,
      role: "presentation",
      style: (0, _extends2.default)({
        width: 'var(--width)'
      }, style)
    });
  }
  let handleFocus = other.onFocus;
  if (process.env.NODE_ENV === 'test' && rootProps.experimentalFeatures?.warnIfFocusStateIsNotSynced) {
    handleFocus = event => {
      const focusedCell = (0, _gridFocusStateSelector.gridFocusCellSelector)(apiRef);
      if (focusedCell?.id === rowId && focusedCell.field === field) {
        if (typeof other.onFocus === 'function') {
          other.onFocus(event);
        }
        return;
      }
      if (!warnedOnce) {
        console.warn([`MUI X: The cell with id=${rowId} and field=${field} received focus.`, `According to the state, the focus should be at id=${focusedCell?.id}, field=${focusedCell?.field}.`, "Not syncing the state may cause unwanted behaviors since the `cellFocusIn` event won't be fired.", 'Call `fireEvent.mouseUp` before the `fireEvent.click` to sync the focus with the state.'].join('\n'));
        warnedOnce = true;
      }
    };
  }
  let children;
  let title;
  if (editCellState === null && column.renderCell) {
    children = column.renderCell(cellParams);
  }
  if (editCellState !== null && column.renderEditCell) {
    const updatedRow = apiRef.current.getRowWithUpdatedValues(rowId, column.field);

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const editCellStateRest = (0, _objectWithoutPropertiesLoose2.default)(editCellState, _excluded2);
    const formattedValue = column.valueFormatter ? column.valueFormatter(editCellState.value, updatedRow, column, apiRef) : cellParams.formattedValue;
    const params = (0, _extends2.default)({}, cellParams, {
      row: updatedRow,
      formattedValue
    }, editCellStateRest);
    children = column.renderEditCell(params);
    classNames.push(_gridClasses.gridClasses['cell--editing']);
    classNames.push(rootClasses?.['cell--editing']);
  }
  if (children === undefined) {
    const valueString = valueToRender?.toString();
    children = valueString;
    title = valueString;
  }
  if (/*#__PURE__*/React.isValidElement(children) && canManageOwnFocus) {
    children = /*#__PURE__*/React.cloneElement(children, {
      focusElementRef
    });
  }
  const draggableEventHandlers = disableDragEvents ? null : {
    onDragEnter: publish('cellDragEnter', onDragEnter),
    onDragOver: publish('cellDragOver', onDragOver)
  };
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", (0, _extends2.default)({
    className: (0, _clsx.default)(classes.root, classNames, className),
    role: "gridcell",
    "data-field": field,
    "data-colindex": colIndex,
    "aria-colindex": colIndex + 1,
    "aria-colspan": colSpan,
    "aria-rowspan": rowSpan,
    style: style,
    title: title,
    tabIndex: tabIndex,
    onClick: publish('cellClick', onClick),
    onDoubleClick: publish('cellDoubleClick', onDoubleClick),
    onMouseOver: publish('cellMouseOver', onMouseOver),
    onMouseDown: publishMouseDown('cellMouseDown'),
    onMouseUp: publishMouseUp('cellMouseUp'),
    onKeyDown: publish('cellKeyDown', onKeyDown),
    onKeyUp: publish('cellKeyUp', onKeyUp)
  }, draggableEventHandlers, other, {
    onFocus: handleFocus,
    ref: handleRef,
    children: children
  }));
});
if (process.env.NODE_ENV !== "production") GridCell.displayName = "GridCell";
process.env.NODE_ENV !== "production" ? GridCell.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  align: _propTypes.default.oneOf(['center', 'left', 'right']).isRequired,
  colIndex: _propTypes.default.number.isRequired,
  colSpan: _propTypes.default.number,
  column: _propTypes.default.object.isRequired,
  disableDragEvents: _propTypes.default.bool,
  isNotVisible: _propTypes.default.bool.isRequired,
  pinnedOffset: _propTypes.default.number,
  pinnedPosition: _propTypes.default.oneOf([0, 1, 2, 3]).isRequired,
  row: _propTypes.default.object.isRequired,
  rowId: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]).isRequired,
  rowNode: _propTypes.default.object.isRequired,
  showLeftBorder: _propTypes.default.bool.isRequired,
  showRightBorder: _propTypes.default.bool.isRequired,
  width: _propTypes.default.number.isRequired
} : void 0;
const MemoizedGridCell = exports.GridCell = (0, _fastMemo.fastMemo)(GridCell);