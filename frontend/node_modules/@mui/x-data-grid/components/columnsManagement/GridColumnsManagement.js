"use strict";
/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridColumnsManagement = GridColumnsManagement;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _debounce = _interopRequireDefault(require("@mui/utils/debounce"));
var _styles = require("@mui/material/styles");
var _InputBase = require("@mui/material/InputBase");
var _cssVariables = require("../../constants/cssVariables");
var _gridColumnsSelector = require("../../hooks/features/columns/gridColumnsSelector");
var _useGridSelector = require("../../hooks/utils/useGridSelector");
var _useGridApiContext = require("../../hooks/utils/useGridApiContext");
var _useGridRootProps = require("../../hooks/utils/useGridRootProps");
var _gridClasses = require("../../constants/gridClasses");
var _utils = require("./utils");
var _assert = require("../../utils/assert");
var _GridShadowScrollArea = require("../GridShadowScrollArea");
var _gridPivotingSelectors = require("../../hooks/features/pivoting/gridPivotingSelectors");
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['columnsManagement'],
    header: ['columnsManagementHeader'],
    searchInput: ['columnsManagementSearchInput'],
    footer: ['columnsManagementFooter'],
    row: ['columnsManagementRow']
  };
  return (0, _composeClasses.default)(slots, _gridClasses.getDataGridUtilityClass, classes);
};
const collator = new Intl.Collator();
function GridColumnsManagement(props) {
  const apiRef = (0, _useGridApiContext.useGridApiContext)();
  const searchInputRef = React.useRef(null);
  const initialColumnVisibilityModel = (0, _useGridSelector.useGridSelector)(apiRef, _gridColumnsSelector.gridInitialColumnVisibilityModelSelector);
  const columnVisibilityModel = (0, _useGridSelector.useGridSelector)(apiRef, _gridColumnsSelector.gridColumnVisibilityModelSelector);
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const [searchValue, setSearchValue] = React.useState('');
  const classes = useUtilityClasses(rootProps);
  const columnDefinitions = (0, _useGridSelector.useGridSelector)(apiRef, _gridColumnsSelector.gridColumnDefinitionsSelector);
  const pivotActive = (0, _useGridSelector.useGridSelector)(apiRef, _gridPivotingSelectors.gridPivotActiveSelector);
  const pivotInitialColumns = (0, _useGridSelector.useGridSelector)(apiRef, _gridPivotingSelectors.gridPivotInitialColumnsSelector);
  const columns = React.useMemo(() => pivotActive ? Array.from(pivotInitialColumns.values()) : columnDefinitions, [pivotActive, pivotInitialColumns, columnDefinitions]);
  const {
    sort,
    searchPredicate = _utils.defaultSearchPredicate,
    autoFocusSearchField = true,
    disableShowHideToggle = false,
    disableResetButton = false,
    toggleAllMode = 'all',
    getTogglableColumns,
    searchInputProps,
    searchDebounceMs = rootProps.columnFilterDebounceMs
  } = props;
  const debouncedFilter = React.useMemo(() => (0, _debounce.default)(value => {
    setSearchValue(value);
  }, searchDebounceMs ?? 150), [searchDebounceMs]);
  const isResetDisabled = React.useMemo(() => (0, _utils.checkColumnVisibilityModelsSame)(columnVisibilityModel, initialColumnVisibilityModel), [columnVisibilityModel, initialColumnVisibilityModel]);
  const sortedColumns = React.useMemo(() => {
    switch (sort) {
      case 'asc':
        return [...columns].sort((a, b) => collator.compare(a.headerName || a.field, b.headerName || b.field));
      case 'desc':
        return [...columns].sort((a, b) => -collator.compare(a.headerName || a.field, b.headerName || b.field));
      default:
        return columns;
    }
  }, [columns, sort]);
  const toggleColumn = event => {
    const {
      name: field
    } = event.target;
    apiRef.current.setColumnVisibility(field, columnVisibilityModel[field] === false);
  };
  const currentColumns = React.useMemo(() => {
    const togglableColumns = getTogglableColumns ? getTogglableColumns(sortedColumns) : null;
    const togglableSortedColumns = togglableColumns ? sortedColumns.filter(({
      field
    }) => togglableColumns.includes(field)) : sortedColumns;
    if (!searchValue) {
      return togglableSortedColumns;
    }
    return togglableSortedColumns.filter(column => searchPredicate(column, searchValue.toLowerCase()));
  }, [sortedColumns, searchValue, searchPredicate, getTogglableColumns]);
  const toggleAllColumns = React.useCallback(isVisible => {
    const currentModel = (0, _gridColumnsSelector.gridColumnVisibilityModelSelector)(apiRef);
    const newModel = (0, _extends2.default)({}, currentModel);
    const togglableColumns = getTogglableColumns ? getTogglableColumns(columns) : null;
    (toggleAllMode === 'filteredOnly' ? currentColumns : columns).forEach(col => {
      if (col.hideable && (togglableColumns == null || togglableColumns.includes(col.field))) {
        if (isVisible) {
          // delete the key from the model instead of setting it to `true`
          delete newModel[col.field];
        } else {
          newModel[col.field] = false;
        }
      }
    });
    return apiRef.current.setColumnVisibilityModel(newModel);
  }, [apiRef, columns, getTogglableColumns, toggleAllMode, currentColumns]);
  const handleSearchValueChange = React.useCallback(event => {
    debouncedFilter(event.target.value);
  }, [debouncedFilter]);
  const hideableColumns = React.useMemo(() => currentColumns.filter(col => col.hideable), [currentColumns]);
  const allHideableColumnsVisible = React.useMemo(() => hideableColumns.every(column => columnVisibilityModel[column.field] == null || columnVisibilityModel[column.field] !== false), [columnVisibilityModel, hideableColumns]);
  const allHideableColumnsHidden = React.useMemo(() => hideableColumns.every(column => columnVisibilityModel[column.field] === false), [columnVisibilityModel, hideableColumns]);
  const firstSwitchRef = React.useRef(null);
  React.useEffect(() => {
    if (autoFocusSearchField) {
      searchInputRef.current?.focus();
    } else if (firstSwitchRef.current && typeof firstSwitchRef.current.focus === 'function') {
      firstSwitchRef.current.focus();
    }
  }, [autoFocusSearchField]);
  let firstHideableColumnFound = false;
  const isFirstHideableColumn = column => {
    if (firstHideableColumnFound === false && column.hideable !== false) {
      firstHideableColumnFound = true;
      return true;
    }
    return false;
  };
  const handleSearchReset = React.useCallback(() => {
    setSearchValue('');
    if (searchInputRef.current) {
      searchInputRef.current.value = '';
      searchInputRef.current.focus();
    }
  }, []);
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(GridColumnsManagementHeader, {
      className: classes.header,
      ownerState: rootProps,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(SearchInput, (0, _extends2.default)({
        as: rootProps.slots.baseTextField,
        ownerState: rootProps,
        placeholder: apiRef.current.getLocaleText('columnsManagementSearchTitle'),
        inputRef: searchInputRef,
        className: classes.searchInput,
        onChange: handleSearchValueChange,
        size: "small",
        type: "search",
        slotProps: {
          input: {
            startAdornment: /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.quickFilterIcon, {
              fontSize: "small"
            }),
            endAdornment: /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseIconButton, (0, _extends2.default)({
              size: "small",
              "aria-label": apiRef.current.getLocaleText('columnsManagementDeleteIconLabel'),
              style: searchValue ? {
                visibility: 'visible'
              } : {
                visibility: 'hidden'
              },
              tabIndex: -1,
              onClick: handleSearchReset,
              edge: "end"
            }, rootProps.slotProps?.baseIconButton, {
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.quickFilterClearIcon, {
                fontSize: "small"
              })
            }))
          },
          htmlInput: {
            'aria-label': apiRef.current.getLocaleText('columnsManagementSearchTitle')
          }
        },
        autoComplete: "off",
        fullWidth: true
      }, rootProps.slotProps?.baseTextField, searchInputProps))
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(GridColumnsManagementScrollArea, {
      ownerState: rootProps,
      children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(GridColumnsManagementBody, {
        className: classes.root,
        ownerState: rootProps,
        children: [currentColumns.map(column => /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseCheckbox, (0, _extends2.default)({
          className: classes.row,
          disabled: column.hideable === false || pivotActive,
          checked: columnVisibilityModel[column.field] !== false,
          onChange: toggleColumn,
          name: column.field,
          inputRef: isFirstHideableColumn(column) ? firstSwitchRef : undefined,
          label: column.headerName || column.field,
          density: "compact",
          fullWidth: true
        }, rootProps.slotProps?.baseCheckbox), column.field)), currentColumns.length === 0 && /*#__PURE__*/(0, _jsxRuntime.jsx)(GridColumnsManagementEmptyText, {
          ownerState: rootProps,
          children: apiRef.current.getLocaleText('columnsManagementNoColumns')
        })]
      })
    }), !disableShowHideToggle || !disableResetButton ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(GridColumnsManagementFooter, {
      ownerState: rootProps,
      className: classes.footer,
      children: [!disableShowHideToggle ? /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseCheckbox, (0, _extends2.default)({
        disabled: hideableColumns.length === 0 || pivotActive,
        checked: allHideableColumnsVisible,
        indeterminate: !allHideableColumnsVisible && !allHideableColumnsHidden,
        onChange: () => toggleAllColumns(!allHideableColumnsVisible),
        name: apiRef.current.getLocaleText('columnsManagementShowHideAllText'),
        label: apiRef.current.getLocaleText('columnsManagementShowHideAllText'),
        density: "compact"
      }, rootProps.slotProps?.baseCheckbox)) : /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {}), !disableResetButton ? /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseButton, (0, _extends2.default)({
        onClick: () => apiRef.current.setColumnVisibilityModel(initialColumnVisibilityModel),
        disabled: isResetDisabled || pivotActive
      }, rootProps.slotProps?.baseButton, {
        children: apiRef.current.getLocaleText('columnsManagementReset')
      })) : null]
    }) : null]
  });
}
process.env.NODE_ENV !== "production" ? GridColumnsManagement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * If `true`, the column search field will be focused automatically.
   * If `false`, the first column switch input will be focused automatically.
   * This helps to avoid input keyboard panel to popup automatically on touch devices.
   * @default true
   */
  autoFocusSearchField: _propTypes.default.bool,
  /**
   * If `true`, the `Reset` button will not be disabled
   * @default false
   */
  disableResetButton: _propTypes.default.bool,
  /**
   * If `true`, the `Show/Hide all` toggle checkbox will not be displayed.
   * @default false
   */
  disableShowHideToggle: _propTypes.default.bool,
  /**
   * Returns the list of togglable columns.
   * If used, only those columns will be displayed in the panel
   * which are passed as the return value of the function.
   * @param {GridColDef[]} columns The `ColDef` list of all columns.
   * @returns {GridColDef['field'][]} The list of togglable columns' field names.
   */
  getTogglableColumns: _propTypes.default.func,
  /**
   * The milliseconds delay to wait after a keystroke before triggering filtering in the columns menu.
   * @default 150
   */
  searchDebounceMs: _propTypes.default.number,
  searchInputProps: _propTypes.default.object,
  searchPredicate: _propTypes.default.func,
  sort: _propTypes.default.oneOf(['asc', 'desc']),
  /**
   * Changes the behavior of the `Show/Hide All` toggle when the search field is used:
   * - `all`: Will toggle all columns.
   * - `filteredOnly`: Will only toggle columns that match the search criteria.
   * @default 'all'
   */
  toggleAllMode: _propTypes.default.oneOf(['all', 'filteredOnly'])
} : void 0;
const GridColumnsManagementBody = (0, _styles.styled)('div', {
  name: 'MuiDataGrid',
  slot: 'ColumnsManagement'
})({
  display: 'flex',
  flexDirection: 'column',
  padding: _cssVariables.vars.spacing(0.5, 1.5)
});
const GridColumnsManagementScrollArea = (0, _styles.styled)(_GridShadowScrollArea.GridShadowScrollArea, {
  name: 'MuiDataGrid',
  slot: 'ColumnsManagementScrollArea'
})({
  maxHeight: 300
});
const GridColumnsManagementHeader = (0, _styles.styled)('div', {
  name: 'MuiDataGrid',
  slot: 'ColumnsManagementHeader'
})({
  padding: _cssVariables.vars.spacing(1.5, 2),
  borderBottom: `1px solid ${_cssVariables.vars.colors.border.base}`
});
const SearchInput = (0, _styles.styled)(_assert.NotRendered, {
  name: 'MuiDataGrid',
  slot: 'ColumnsManagementSearchInput'
})({
  [`& .${_InputBase.inputBaseClasses.input}::-webkit-search-decoration,
      & .${_InputBase.inputBaseClasses.input}::-webkit-search-cancel-button,
      & .${_InputBase.inputBaseClasses.input}::-webkit-search-results-button,
      & .${_InputBase.inputBaseClasses.input}::-webkit-search-results-decoration`]: {
    /* clears the 'X' icon from Chrome */
    display: 'none'
  }
});
const GridColumnsManagementFooter = (0, _styles.styled)('div', {
  name: 'MuiDataGrid',
  slot: 'ColumnsManagementFooter'
})({
  padding: _cssVariables.vars.spacing(1, 1, 1, 1.5),
  display: 'flex',
  justifyContent: 'space-between',
  borderTop: `1px solid ${_cssVariables.vars.colors.border.base}`
});
const GridColumnsManagementEmptyText = (0, _styles.styled)('div', {
  name: 'MuiDataGrid',
  slot: 'ColumnsManagementEmptyText'
})({
  padding: _cssVariables.vars.spacing(1, 0),
  alignSelf: 'center',
  font: _cssVariables.vars.typography.font.body
});