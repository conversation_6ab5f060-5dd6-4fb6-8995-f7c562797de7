"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridColumnMenuSortItem = GridColumnMenuSortItem;
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _useGridSelector = require("../../../../hooks/utils/useGridSelector");
var _gridSortingSelector = require("../../../../hooks/features/sorting/gridSortingSelector");
var _useGridApiContext = require("../../../../hooks/utils/useGridApiContext");
var _useGridRootProps = require("../../../../hooks/utils/useGridRootProps");
var _jsxRuntime = require("react/jsx-runtime");
function GridColumnMenuSortItem(props) {
  const {
    colDef,
    onClick
  } = props;
  const apiRef = (0, _useGridApiContext.useGridApiContext)();
  const sortModel = (0, _useGridSelector.useGridSelector)(apiRef, _gridSortingSelector.gridSortModelSelector);
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const sortDirection = React.useMemo(() => {
    if (!colDef) {
      return null;
    }
    const sortItem = sortModel.find(item => item.field === colDef.field);
    return sortItem?.sort;
  }, [colDef, sortModel]);
  const sortingOrder = colDef.sortingOrder ?? rootProps.sortingOrder;
  const onSortMenuItemClick = React.useCallback(event => {
    onClick(event);
    const direction = event.currentTarget.getAttribute('data-value') || null;
    apiRef.current.sortColumn(colDef.field, direction === sortDirection ? null : direction);
  }, [apiRef, colDef, onClick, sortDirection]);
  if (rootProps.disableColumnSorting || !colDef || !colDef.sortable || !sortingOrder.some(item => !!item)) {
    return null;
  }
  const getLabel = key => {
    const label = apiRef.current.getLocaleText(key);
    return typeof label === 'function' ? label(colDef) : label;
  };
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
    children: [sortingOrder.includes('asc') && sortDirection !== 'asc' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseMenuItem, {
      onClick: onSortMenuItemClick,
      "data-value": "asc",
      iconStart: /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.columnMenuSortAscendingIcon, {
        fontSize: "small"
      }),
      children: getLabel('columnMenuSortAsc')
    }) : null, sortingOrder.includes('desc') && sortDirection !== 'desc' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseMenuItem, {
      onClick: onSortMenuItemClick,
      "data-value": "desc",
      iconStart: /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.columnMenuSortDescendingIcon, {
        fontSize: "small"
      }),
      children: getLabel('columnMenuSortDesc')
    }) : null, sortingOrder.includes(null) && sortDirection != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseMenuItem, {
      onClick: onSortMenuItemClick,
      iconStart: rootProps.slots.columnMenuUnsortIcon ? /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.columnMenuUnsortIcon, {
        fontSize: "small"
      }) : /*#__PURE__*/(0, _jsxRuntime.jsx)("span", {}),
      children: apiRef.current.getLocaleText('columnMenuUnsort')
    }) : null]
  });
}
process.env.NODE_ENV !== "production" ? GridColumnMenuSortItem.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  colDef: _propTypes.default.object.isRequired,
  onClick: _propTypes.default.func.isRequired
} : void 0;