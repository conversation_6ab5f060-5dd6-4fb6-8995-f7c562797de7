"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridVirtualScroller = GridVirtualScroller;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _system = require("@mui/system");
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _gridDimensionsSelectors = require("../../hooks/features/dimensions/gridDimensionsSelectors");
var _rows = require("../../hooks/features/rows");
var _GridScrollArea = require("../GridScrollArea");
var _useGridRootProps = require("../../hooks/utils/useGridRootProps");
var _useGridPrivateApiContext = require("../../hooks/utils/useGridPrivateApiContext");
var _useGridSelector = require("../../hooks/utils/useGridSelector");
var _gridClasses = require("../../constants/gridClasses");
var _useGridOverlays = require("../../hooks/features/overlays/useGridOverlays");
var _GridHeaders = require("../GridHeaders");
var _GridMainContainer = require("./GridMainContainer");
var _GridTopContainer = require("./GridTopContainer");
var _GridVirtualScrollerContent = require("./GridVirtualScrollerContent");
var _GridVirtualScrollerFiller = require("./GridVirtualScrollerFiller");
var _GridVirtualScrollerRenderZone = require("./GridVirtualScrollerRenderZone");
var _GridVirtualScrollbar = require("./GridVirtualScrollbar");
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = ownerState => {
  const {
    classes,
    hasScrollX,
    hasPinnedRight,
    loadingOverlayVariant,
    overlayType
  } = ownerState;
  const hideContent = loadingOverlayVariant === 'skeleton' || overlayType === 'noColumnsOverlay';
  const slots = {
    root: ['main', hasPinnedRight && 'main--hasPinnedRight', hideContent && 'main--hiddenContent'],
    scroller: ['virtualScroller', hasScrollX && 'virtualScroller--hasScrollX']
  };
  return (0, _composeClasses.default)(slots, _gridClasses.getDataGridUtilityClass, classes);
};
const Scroller = (0, _system.styled)('div', {
  name: 'MuiDataGrid',
  slot: 'VirtualScroller',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.virtualScroller, ownerState.hasScrollX && styles['virtualScroller--hasScrollX']];
  }
})({
  position: 'relative',
  height: '100%',
  flexGrow: 1,
  overflow: 'scroll',
  scrollbarWidth: 'none' /* Firefox */,
  display: 'flex',
  flexDirection: 'column',
  '&::-webkit-scrollbar': {
    display: 'none' /* Safari and Chrome */
  },
  '@media print': {
    overflow: 'hidden'
  },
  // See https://github.com/mui/mui-x/issues/10547
  zIndex: 0
});
const hasPinnedRightSelector = apiRef => apiRef.current.state.dimensions.rightPinnedWidth > 0;
function GridVirtualScroller(props) {
  const apiRef = (0, _useGridPrivateApiContext.useGridPrivateApiContext)();
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const hasScrollY = (0, _useGridSelector.useGridSelector)(apiRef, _gridDimensionsSelectors.gridHasScrollYSelector);
  const hasScrollX = (0, _useGridSelector.useGridSelector)(apiRef, _gridDimensionsSelectors.gridHasScrollXSelector);
  const hasPinnedRight = (0, _useGridSelector.useGridSelector)(apiRef, hasPinnedRightSelector);
  const hasBottomFiller = (0, _useGridSelector.useGridSelector)(apiRef, _gridDimensionsSelectors.gridHasBottomFillerSelector);
  const {
    getOverlay,
    overlaysProps
  } = (0, _useGridOverlays.useGridOverlays)();
  const ownerState = (0, _extends2.default)({
    classes: rootProps.classes,
    hasScrollX,
    hasPinnedRight
  }, overlaysProps);
  const classes = useUtilityClasses(ownerState);
  const virtualScroller = apiRef.current.virtualizer.api.useVirtualization().getters;
  const {
    getContainerProps,
    getScrollerProps,
    getContentProps,
    getRenderZoneProps,
    getScrollbarVerticalProps,
    getScrollbarHorizontalProps,
    getRows,
    getScrollAreaProps
  } = virtualScroller;
  const rows = getRows(undefined, (0, _rows.gridRowTreeSelector)(apiRef));
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_GridMainContainer.GridMainContainer, (0, _extends2.default)({
    className: classes.root
  }, getContainerProps(), {
    ownerState: ownerState,
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_GridScrollArea.GridScrollArea, (0, _extends2.default)({
      scrollDirection: "left"
    }, getScrollAreaProps())), /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridScrollArea.GridScrollArea, (0, _extends2.default)({
      scrollDirection: "right"
    }, getScrollAreaProps())), /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridScrollArea.GridScrollArea, (0, _extends2.default)({
      scrollDirection: "up"
    }, getScrollAreaProps())), /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridScrollArea.GridScrollArea, (0, _extends2.default)({
      scrollDirection: "down"
    }, getScrollAreaProps())), /*#__PURE__*/(0, _jsxRuntime.jsxs)(Scroller, (0, _extends2.default)({
      className: classes.scroller
    }, getScrollerProps(), {
      ownerState: ownerState,
      children: [/*#__PURE__*/(0, _jsxRuntime.jsxs)(_GridTopContainer.GridTopContainer, {
        children: [!rootProps.listView && /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridHeaders.GridHeaders, {}), /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.pinnedRows, {
          position: "top",
          virtualScroller: virtualScroller
        })]
      }), getOverlay(), /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridVirtualScrollerContent.GridVirtualScrollerContent, (0, _extends2.default)({}, getContentProps(), {
        children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_GridVirtualScrollerRenderZone.GridVirtualScrollerRenderZone, (0, _extends2.default)({}, getRenderZoneProps(), {
          children: [rows, /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.detailPanels, {
            virtualScroller: virtualScroller
          })]
        }))
      })), hasBottomFiller && /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridVirtualScrollerFiller.GridVirtualScrollerFiller, {
        rowsLength: rows.length
      }), /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.bottomContainer, {
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.pinnedRows, {
          position: "bottom",
          virtualScroller: virtualScroller
        })
      })]
    })), hasScrollX && !rootProps.listView && /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridVirtualScrollbar.GridVirtualScrollbar, (0, _extends2.default)({
      position: "horizontal"
    }, getScrollbarHorizontalProps())), hasScrollY && /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridVirtualScrollbar.GridVirtualScrollbar, (0, _extends2.default)({
      position: "vertical"
    }, getScrollbarVerticalProps())), props.children]
  }));
}