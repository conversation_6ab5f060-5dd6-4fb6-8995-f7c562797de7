"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridVirtualScrollerRenderZone = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _clsx = _interopRequireDefault(require("clsx"));
var _system = require("@mui/system");
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _forwardRef = require("@mui/x-internals/forwardRef");
var _useGridApiContext = require("../../hooks/utils/useGridApiContext");
var _useGridSelector = require("../../hooks/utils/useGridSelector");
var _rows = require("../../hooks/features/rows");
var _virtualization = require("../../hooks/features/virtualization");
var _useGridRootProps = require("../../hooks/utils/useGridRootProps");
var _gridClasses = require("../../constants/gridClasses");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["className"];
const useUtilityClasses = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['virtualScrollerRenderZone']
  };
  return (0, _composeClasses.default)(slots, _gridClasses.getDataGridUtilityClass, classes);
};
const VirtualScrollerRenderZoneRoot = (0, _system.styled)('div', {
  name: 'MuiDataGrid',
  slot: 'VirtualScrollerRenderZone'
})({
  position: 'absolute',
  display: 'flex',
  // Prevents margin collapsing when using `getRowSpacing`
  flexDirection: 'column'
});
const GridVirtualScrollerRenderZone = exports.GridVirtualScrollerRenderZone = (0, _forwardRef.forwardRef)(function GridVirtualScrollerRenderZone(props, ref) {
  const {
      className
    } = props,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const apiRef = (0, _useGridApiContext.useGridApiContext)();
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const classes = useUtilityClasses(rootProps);
  const offsetTop = (0, _useGridSelector.useGridSelector)(apiRef, () => {
    const renderContext = (0, _virtualization.gridRenderContextSelector)(apiRef);
    const rowsMeta = (0, _rows.gridRowsMetaSelector)(apiRef);
    return rowsMeta.positions[renderContext.firstRowIndex] ?? 0;
  });
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(VirtualScrollerRenderZoneRoot, (0, _extends2.default)({
    className: (0, _clsx.default)(classes.root, className),
    ownerState: rootProps,
    style: {
      transform: `translate3d(0, ${offsetTop}px, 0)`
    }
  }, other, {
    ref: ref
  }));
});
if (process.env.NODE_ENV !== "production") GridVirtualScrollerRenderZone.displayName = "GridVirtualScrollerRenderZone";