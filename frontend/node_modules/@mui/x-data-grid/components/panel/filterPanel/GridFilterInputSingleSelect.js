"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridFilterInputSingleSelect = GridFilterInputSingleSelect;
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _propTypes = _interopRequireDefault(require("prop-types"));
var _useId = _interopRequireDefault(require("@mui/utils/useId"));
var _useGridRootProps = require("../../../hooks/utils/useGridRootProps");
var _filterPanelUtils = require("./filterPanelUtils");
var _jsxRuntime = require("react/jsx-runtime");
const _excluded = ["item", "applyValue", "type", "apiRef", "focusElementRef", "tabIndex", "isFilterActive", "clearButton", "headerFilterMenu", "slotProps"];
const renderSingleSelectOptions = ({
  column,
  OptionComponent,
  getOptionLabel,
  getOptionValue,
  isSelectNative,
  baseSelectOptionProps
}) => {
  const iterableColumnValues = ['', ...((0, _filterPanelUtils.getValueOptions)(column) || [])];
  return iterableColumnValues.map(option => {
    const value = getOptionValue(option);
    let label = getOptionLabel(option);
    if (label === '') {
      label = ' '; // To force the height of the empty option
    }
    return /*#__PURE__*/(0, _react.createElement)(OptionComponent, (0, _extends2.default)({}, baseSelectOptionProps, {
      native: isSelectNative,
      key: value,
      value: value
    }), label);
  });
};
function GridFilterInputSingleSelect(props) {
  const {
      item,
      applyValue,
      type,
      apiRef,
      focusElementRef,
      tabIndex,
      clearButton,
      headerFilterMenu,
      slotProps
    } = props,
    others = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  const filterValue = item.value ?? '';
  const id = (0, _useId.default)();
  const labelId = (0, _useId.default)();
  const rootProps = (0, _useGridRootProps.useGridRootProps)();
  const isSelectNative = rootProps.slotProps?.baseSelect?.native ?? false;
  let resolvedColumn = null;
  if (item.field) {
    const column = apiRef.current.getColumn(item.field);
    if ((0, _filterPanelUtils.isSingleSelectColDef)(column)) {
      resolvedColumn = column;
    }
  }
  const getOptionValue = resolvedColumn?.getOptionValue;
  const getOptionLabel = resolvedColumn?.getOptionLabel;
  const currentValueOptions = React.useMemo(() => {
    return (0, _filterPanelUtils.getValueOptions)(resolvedColumn);
  }, [resolvedColumn]);
  const onFilterChange = React.useCallback(event => {
    let value = event.target.value;

    // NativeSelect casts the value to a string.
    value = (0, _filterPanelUtils.getValueFromValueOptions)(value, currentValueOptions, getOptionValue);
    applyValue((0, _extends2.default)({}, item, {
      value
    }));
  }, [currentValueOptions, getOptionValue, applyValue, item]);
  if (!(0, _filterPanelUtils.isSingleSelectColDef)(resolvedColumn)) {
    return null;
  }
  const label = slotProps?.root.label ?? apiRef.current.getLocaleText('filterPanelInputLabel');
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(rootProps.slots.baseSelect, (0, _extends2.default)({
      fullWidth: true,
      id: id,
      label: label,
      labelId: labelId,
      value: filterValue,
      onChange: onFilterChange,
      slotProps: {
        htmlInput: (0, _extends2.default)({
          tabIndex,
          ref: focusElementRef,
          type: type || 'text',
          placeholder: slotProps?.root.placeholder ?? apiRef.current.getLocaleText('filterPanelInputPlaceholder')
        }, slotProps?.root.slotProps?.htmlInput)
      },
      native: isSelectNative
    }, rootProps.slotProps?.baseSelect, others, slotProps?.root, {
      children: renderSingleSelectOptions({
        column: resolvedColumn,
        OptionComponent: rootProps.slots.baseSelectOption,
        getOptionLabel,
        getOptionValue,
        isSelectNative,
        baseSelectOptionProps: rootProps.slotProps?.baseSelectOption
      })
    })), headerFilterMenu, clearButton]
  });
}
process.env.NODE_ENV !== "production" ? GridFilterInputSingleSelect.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: _propTypes.default.shape({
    current: _propTypes.default.object.isRequired
  }).isRequired,
  applyValue: _propTypes.default.func.isRequired,
  className: _propTypes.default.string,
  clearButton: _propTypes.default.node,
  disabled: _propTypes.default.bool,
  focusElementRef: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOfType([_propTypes.default.func, _propTypes.default.object]),
  headerFilterMenu: _propTypes.default.node,
  inputRef: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.shape({
    current: (props, propName) => {
      if (props[propName] == null) {
        return null;
      }
      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {
        return new Error(`Expected prop '${propName}' to be of type Element`);
      }
      return null;
    }
  })]),
  /**
   * It is `true` if the filter either has a value or an operator with no value
   * required is selected (for example `isEmpty`)
   */
  isFilterActive: _propTypes.default.bool,
  item: _propTypes.default.shape({
    field: _propTypes.default.string.isRequired,
    id: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]),
    operator: _propTypes.default.string.isRequired,
    value: _propTypes.default.any
  }).isRequired,
  onBlur: _propTypes.default.func,
  onFocus: _propTypes.default.func,
  slotProps: _propTypes.default.object,
  tabIndex: _propTypes.default.number,
  type: _propTypes.default.oneOf(['singleSelect'])
} : void 0;