"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridApiContext = void 0;
var React = _interopRequireWildcard(require("react"));
const GridApiContext = exports.GridApiContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== "production") GridApiContext.displayName = "GridApiContext";