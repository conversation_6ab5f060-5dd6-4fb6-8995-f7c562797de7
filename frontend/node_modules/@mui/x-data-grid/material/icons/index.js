"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridVisibilityOffIcon = exports.GridViewStreamIcon = exports.GridViewHeadlineIcon = exports.GridViewColumnIcon = exports.GridTripleDotsVerticalIcon = exports.GridTableRowsIcon = exports.GridSeparatorIcon = exports.GridSearchIcon = exports.GridRemoveIcon = exports.GridMoreVertIcon = exports.GridMenuIcon = exports.GridLoadIcon = exports.GridKeyboardArrowRight = exports.GridFilterListIcon = exports.GridFilterAltIcon = exports.GridExpandMoreIcon = exports.GridDragIcon = exports.GridDownloadIcon = exports.GridDeleteIcon = exports.GridDeleteForeverIcon = exports.GridColumnIcon = exports.GridCloseIcon = exports.GridClearIcon = exports.GridCheckIcon = exports.GridCheckCircleIcon = exports.GridArrowUpwardIcon = exports.GridArrowDownwardIcon = exports.GridAddIcon = void 0;
var React = _interopRequireWildcard(require("react"));
var _createSvgIcon = require("./createSvgIcon");
var _jsxRuntime = require("react/jsx-runtime");
const GridArrowUpwardIcon = exports.GridArrowUpwardIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"
}), 'ArrowUpward');
const GridArrowDownwardIcon = exports.GridArrowDownwardIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"
}), 'ArrowDownward');
const GridKeyboardArrowRight = exports.GridKeyboardArrowRight = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M8.59 16.59 13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
}), 'KeyboardArrowRight');
const GridExpandMoreIcon = exports.GridExpandMoreIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"
}), 'ExpandMore');
const GridFilterListIcon = exports.GridFilterListIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"
}), 'FilterList');
const GridFilterAltIcon = exports.GridFilterAltIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.72-4.8 5.74-7.39c.51-.66.04-1.61-.79-1.61H5.04c-.83 0-1.3.95-.79 1.61z"
}), 'FilterAlt');
const GridSearchIcon = exports.GridSearchIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
}), 'Search');
const GridMenuIcon = exports.GridMenuIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"
}), 'Menu');
const GridCheckCircleIcon = exports.GridCheckCircleIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
}), 'CheckCircle');
const GridColumnIcon = exports.GridColumnIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M14.67 5v14H9.33V5zm1 14H21V5h-5.33zm-7.34 0V5H3v14z"
}), 'ColumnIcon');
const GridSeparatorIcon = exports.GridSeparatorIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("rect", {
  width: "1",
  height: "24",
  x: "11.5",
  rx: "0.5"
}), 'Separator');
const GridViewHeadlineIcon = exports.GridViewHeadlineIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M4 15h16v-2H4v2zm0 4h16v-2H4v2zm0-8h16V9H4v2zm0-6v2h16V5H4z"
}), 'ViewHeadline');
const GridTableRowsIcon = exports.GridTableRowsIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M21,8H3V4h18V8z M21,10H3v4h18V10z M21,16H3v4h18V16z"
}), 'TableRows');
const GridViewStreamIcon = exports.GridViewStreamIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M4 18h17v-6H4v6zM4 5v6h17V5H4z"
}), 'ViewStream');
const GridTripleDotsVerticalIcon = exports.GridTripleDotsVerticalIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
}), 'TripleDotsVertical');
const GridCloseIcon = exports.GridCloseIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
}), 'Close');
const GridAddIcon = exports.GridAddIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"
}), 'Add');
const GridRemoveIcon = exports.GridRemoveIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M19 13H5v-2h14v2z"
}), 'Remove');
const GridLoadIcon = exports.GridLoadIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"
}), 'Load');
const GridDragIcon = exports.GridDragIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
}), 'Drag');
const GridCheckIcon = exports.GridCheckIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"
}), 'Check');
const GridMoreVertIcon = exports.GridMoreVertIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
}), 'MoreVert');
const GridVisibilityOffIcon = exports.GridVisibilityOffIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"
}), 'VisibilityOff');
const GridViewColumnIcon = exports.GridViewColumnIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("g", {
  children: /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
    d: "M14.67,5v14H9.33V5H14.67z M15.67,19H21V5h-5.33V19z M8.33,19V5H3v14H8.33z"
  })
}), 'ViewColumn');
const GridClearIcon = exports.GridClearIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"
}), 'Clear');
const GridDeleteIcon = exports.GridDeleteIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
}), 'Delete');
const GridDeleteForeverIcon = exports.GridDeleteForeverIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zm2.46-7.12l1.41-1.41L12 12.59l2.12-2.12 1.41 1.41L13.41 14l2.12 2.12-1.41 1.41L12 15.41l-2.12 2.12-1.41-1.41L10.59 14l-2.13-2.12zM15.5 4l-1-1h-5l-1 1H5v2h14V4z"
}), 'Delete');
const GridDownloadIcon = exports.GridDownloadIcon = (0, _createSvgIcon.createSvgIcon)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"
}), 'Download');