"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.gridRowIdSelector = void 0;
var _gridRowsUtils = require("../features/rows/gridRowsUtils");
var _createSelector = require("../../utils/createSelector");
/**
 * Get the row id for a given row
 * @param apiRef - The grid api reference
 * @param {GridRowModel} row - The row to get the id for
 * @returns {GridRowId} The row id
 */
const gridRowIdSelector = exports.gridRowIdSelector = (0, _createSelector.createRootSelector)((state, row) => {
  if (_gridRowsUtils.GRID_ID_AUTOGENERATED in row) {
    return row[_gridRowsUtils.GRID_ID_AUTOGENERATED];
  }
  return state.props.getRowId ? state.props.getRowId(row) : row.id;
});