"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridDataSourceCacheDefault = void 0;
exports.getKeyDefault = getKeyDefault;
function getKeyDefault(params) {
  return JSON.stringify([params.filterModel, params.sortModel, params.start, params.end]);
}
class GridDataSourceCacheDefault {
  constructor({
    ttl = 300_000,
    getKey = getKeyDefault
  }) {
    this.cache = void 0;
    this.ttl = void 0;
    this.getKey = void 0;
    this.cache = {};
    this.ttl = ttl;
    this.getKey = getKey;
  }
  set(key, value) {
    const keyString = this.getKey(key);
    const expiry = Date.now() + this.ttl;
    this.cache[keyString] = {
      value,
      expiry
    };
  }
  get(key) {
    const keyString = this.getKey(key);
    const entry = this.cache[keyString];
    if (!entry) {
      return undefined;
    }
    if (Date.now() > entry.expiry) {
      delete this.cache[keyString];
      return undefined;
    }
    return entry.value;
  }
  clear() {
    this.cache = {};
  }
}
exports.GridDataSourceCacheDefault = GridDataSourceCacheDefault;