"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GRID_CHECKBOX_SELECTION_FIELD = exports.GRID_CHECKBOX_SELECTION_COL_DEF = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _GridCellCheckboxRenderer = require("../components/columnSelection/GridCellCheckboxRenderer");
var _GridHeaderCheckbox = require("../components/columnSelection/GridHeaderCheckbox");
var _gridBooleanColDef = require("./gridBooleanColDef");
var _gridPropsSelectors = require("../hooks/core/gridPropsSelectors");
var _jsxRuntime = require("react/jsx-runtime");
const GRID_CHECKBOX_SELECTION_FIELD = exports.GRID_CHECKBOX_SELECTION_FIELD = '__check__';
const GRID_CHECKBOX_SELECTION_COL_DEF = exports.GRID_CHECKBOX_SELECTION_COL_DEF = (0, _extends2.default)({}, _gridBooleanColDef.GRID_BOOLEAN_COL_DEF, {
  type: 'custom',
  field: GRID_CHECKBOX_SELECTION_FIELD,
  width: 50,
  resizable: false,
  sortable: false,
  filterable: false,
  // @ts-ignore
  aggregable: false,
  disableColumnMenu: true,
  disableReorder: true,
  disableExport: true,
  getApplyQuickFilterFn: () => null,
  display: 'flex',
  valueGetter: (value, row, column, apiRef) => {
    const rowId = (0, _gridPropsSelectors.gridRowIdSelector)(apiRef, row);
    return apiRef.current.isRowSelected(rowId);
  },
  rowSpanValueGetter: (_, row, column, apiRef) => (0, _gridPropsSelectors.gridRowIdSelector)(apiRef, row),
  renderHeader: params => /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridHeaderCheckbox.GridHeaderCheckbox, (0, _extends2.default)({}, params)),
  renderCell: params => /*#__PURE__*/(0, _jsxRuntime.jsx)(_GridCellCheckboxRenderer.GridCellCheckboxRenderer, (0, _extends2.default)({}, params))
});