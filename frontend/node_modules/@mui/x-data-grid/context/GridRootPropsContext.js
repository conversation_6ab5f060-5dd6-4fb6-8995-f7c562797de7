"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridRootPropsContext = void 0;
var React = _interopRequireWildcard(require("react"));
const GridRootPropsContext = exports.GridRootPropsContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== "production") GridRootPropsContext.displayName = "GridRootPropsContext";