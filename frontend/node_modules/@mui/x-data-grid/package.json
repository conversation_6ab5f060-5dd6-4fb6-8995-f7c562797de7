{"name": "@mui/x-data-grid", "version": "8.9.2", "author": "MUI Team", "description": "The Community plan edition of the MUI X Data Grid components.", "main": "./index.js", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com/x/react-data-grid/", "sideEffects": ["**/*.css"], "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "publishConfig": {"access": "public"}, "keywords": ["react", "react-component", "material-ui", "mui", "mui-x", "react-table", "table", "datatable", "data-table", "datagrid", "data-grid"], "repository": {"type": "git", "url": "git+https://github.com/mui/mui-x.git", "directory": "packages/x-data-grid"}, "dependencies": {"@babel/runtime": "^7.28.2", "@mui/utils": "^7.2.0", "clsx": "^2.1.1", "prop-types": "^15.8.1", "use-sync-external-store": "^1.5.0", "@mui/x-internals": "8.9.2", "@mui/x-virtualizer": "0.1.0"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.15.14 || ^6.0.0 || ^7.0.0", "@mui/system": "^5.15.14 || ^6.0.0 || ^7.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "engines": {"node": ">=14.0.0"}, "private": false, "module": "./esm/index.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}, "types": "./index.d.ts"}