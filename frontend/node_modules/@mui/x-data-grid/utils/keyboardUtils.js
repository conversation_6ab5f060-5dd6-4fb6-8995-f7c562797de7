"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isCellExitEditModeKeys = exports.isCellEnterEditModeKeys = exports.isCellEditCommitKeys = exports.GRID_MULTIPLE_SELECTION_KEYS = exports.GRID_CELL_EXIT_EDIT_MODE_KEYS = exports.GRID_CELL_EDIT_COMMIT_KEYS = void 0;
exports.isCopyShortcut = isCopyShortcut;
exports.isNavigationKey = exports.isMultipleKey = exports.isKeyboardEvent = exports.isHideMenuKey = void 0;
exports.isPasteShortcut = isPasteShortcut;
exports.isPrintableKey = isPrintableKey;
// Non printable keys have a name, for example "ArrowRight", see the whole list:
// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_key_values
// So event.key.length === 1 is often enough.
//
// However, we also need to ignore shortcuts, for example: select all:
// - Windows: Ctrl+A, event.ctrlKey is true
// - macOS: ⌘ Command+A, event.metaKey is true
function isPrintableKey(event) {
  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;
}
const GRID_MULTIPLE_SELECTION_KEYS = exports.GRID_MULTIPLE_SELECTION_KEYS = ['Meta', 'Control', 'Shift'];
const GRID_CELL_EXIT_EDIT_MODE_KEYS = exports.GRID_CELL_EXIT_EDIT_MODE_KEYS = ['Enter', 'Escape', 'Tab'];
const GRID_CELL_EDIT_COMMIT_KEYS = exports.GRID_CELL_EDIT_COMMIT_KEYS = ['Enter', 'Tab'];
const isMultipleKey = key => GRID_MULTIPLE_SELECTION_KEYS.indexOf(key) > -1;
exports.isMultipleKey = isMultipleKey;
const isCellEnterEditModeKeys = event => isPrintableKey(event) || event.key === 'Enter' || event.key === 'Backspace' || event.key === 'Delete';
exports.isCellEnterEditModeKeys = isCellEnterEditModeKeys;
const isCellExitEditModeKeys = key => GRID_CELL_EXIT_EDIT_MODE_KEYS.indexOf(key) > -1;
exports.isCellExitEditModeKeys = isCellExitEditModeKeys;
const isCellEditCommitKeys = key => GRID_CELL_EDIT_COMMIT_KEYS.indexOf(key) > -1;
exports.isCellEditCommitKeys = isCellEditCommitKeys;
const isNavigationKey = key => key.indexOf('Arrow') === 0 || key.indexOf('Page') === 0 || key === ' ' || key === 'Home' || key === 'End';
exports.isNavigationKey = isNavigationKey;
const isKeyboardEvent = event => !!event.key;
exports.isKeyboardEvent = isKeyboardEvent;
const isHideMenuKey = key => key === 'Tab' || key === 'Escape';

// In theory, on macOS, ctrl + v doesn't trigger a paste, so the function should return false.
// However, maybe it's overkill to fix, so let's be lazy.
exports.isHideMenuKey = isHideMenuKey;
function isPasteShortcut(event) {
  return (event.ctrlKey || event.metaKey) &&
  // We can't use event.code === 'KeyV' as event.code assumes a QWERTY keyboard layout,
  // for example, it would be another letter on a Dvorak physical keyboard.
  // We can't use event.key === 'v' as event.key is not stable with key modifiers and keyboard layouts,
  // for example, it would be ה on a Hebrew keyboard layout.
  // https://github.com/w3c/uievents/issues/377 could be a long-term solution
  String.fromCharCode(event.keyCode) === 'V' && !event.shiftKey && !event.altKey;
}

// Checks if the keyboard event corresponds to the copy shortcut (CTRL+C or CMD+C) across different localization keyboards.
function isCopyShortcut(event) {
  return (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'C' && !event.shiftKey && !event.altKey;
}