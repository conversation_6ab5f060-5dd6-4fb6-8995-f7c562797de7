# MUI X Data Grid

This package is the Community plan edition of the Data Grid components.
It's part of [MUI X](https://mui.com/x/), an open-core extension of our Core libraries, with advanced components.

## Installation

Install the package in your project directory with:

```bash
npm install @mui/x-data-grid
```

This component has the following peer dependencies that you need to install as well.

```json
"peerDependencies": {
  "@mui/material": "^5.15.14 || ^6.0.0 || ^7.0.0",
  "react": "^17.0.0 || ^18.0.0 || ^19.0.0",
  "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"
},
```

## Documentation

Visit [https://mui.com/x/react-data-grid/](https://mui.com/x/react-data-grid/) to view the full documentation.
