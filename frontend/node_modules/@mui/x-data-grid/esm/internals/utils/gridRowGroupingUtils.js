import { GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD } from "../constants.js";
export const getRowGroupingCriteriaFromGroupingField = groupingColDefField => {
  const match = groupingColDefField.match(/^__row_group_by_columns_group_(.*)__$/);
  if (!match) {
    return null;
  }
  return match[1];
};
export const isGroupingColumn = field => field === GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD || getRowGroupingCriteriaFromGroupingField(field) !== null;