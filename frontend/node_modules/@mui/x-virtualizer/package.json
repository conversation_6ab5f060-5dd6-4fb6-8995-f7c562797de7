{"name": "@mui/x-virtualizer", "version": "0.1.0", "author": "MUI Team", "description": "MUI virtualization library", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com", "sideEffects": false, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "publishConfig": {"access": "public"}, "keywords": ["react", "react-component", "virtualization"], "repository": {"type": "git", "url": "git+https://github.com/mui/mui-x.git", "directory": "packages/x-virtualizer"}, "dependencies": {"@babel/runtime": "^7.27.4", "@mui/x-internals": "8.9.2"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "engines": {"node": ">=14.0.0"}, "private": false, "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}, "types": "./index.d.ts"}