/**
 * @mui/x-virtualizer v0.1.0
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _useVirtualizer = require("./useVirtualizer");
Object.keys(_useVirtualizer).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _useVirtualizer[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _useVirtualizer[key];
    }
  });
});
var _features = require("./features");
Object.keys(_features).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _features[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _features[key];
    }
  });
});