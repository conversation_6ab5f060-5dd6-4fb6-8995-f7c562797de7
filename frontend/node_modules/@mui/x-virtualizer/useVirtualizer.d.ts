import * as React from 'react';
import { integer, RefObject } from '@mui/x-internals/types';
import { Store } from '@mui/x-internals/store';
import { Colspan } from "./features/colspan.js";
import { Dimensions } from "./features/dimensions.js";
import { Rowspan } from "./features/rowspan.js";
import { Virtualization } from "./features/virtualization.js";
import type { RowId } from "./models/core.js";
import type { HeightEntry, RowSpacing, RowVisibilityParams } from "./models/dimensions.js";
import { ColumnWithWidth, FocusedCell, Size, PinnedRows, PinnedColumns, RenderContext, Row, RowEntry } from "./models/index.js";
export type Virtualizer = ReturnType<typeof useVirtualizer>;
export type VirtualScrollerCompat = Virtualization.State['getters'];
export type BaseState = Virtualization.State & Dimensions.State;
export type VirtualizerParams = {
  scrollbarSize?: number;
  dimensions: {
    rowHeight: number;
    headerHeight: number;
    groupHeaderHeight: number;
    headerFilterHeight: number;
    columnsTotalWidth: number;
    headersTotalHeight: number;
    leftPinnedWidth: number;
    rightPinnedWidth: number;
  };
  initialState?: {
    scroll?: {
      top: number;
      left: number;
    };
    dimensions?: Partial<Dimensions.State['dimensions']>;
    rowSpanning?: Rowspan.State['rowSpanning'];
    virtualization?: Partial<Virtualization.State['virtualization']>;
  };
  isRtl: boolean;
  /** current page rows */
  rows: RowEntry[];
  /** current page range */
  range: {
    firstRowIndex: integer;
    lastRowIndex: integer;
  } | null;
  rowIdToIndexMap: Map<RowId, number>;
  rowCount: integer;
  columns: ColumnWithWidth[];
  pinnedRows: PinnedRows;
  pinnedColumns: PinnedColumns;
  refs: {
    container: RefObject<HTMLDivElement | null>;
    scroller: RefObject<HTMLDivElement | null>;
    scrollbarVertical: RefObject<HTMLDivElement | null>;
    scrollbarHorizontal: RefObject<HTMLDivElement | null>;
  };
  hasColSpan: boolean;
  contentHeight: number;
  minimalContentHeight: number | string;
  autoHeight: boolean;
  getRowHeight?: (row: RowEntry) => number | null | undefined | 'auto';
  /**
   * Function that returns the estimated height for a row.
   * Only works if dynamic row height is used.
   * Once the row height is measured this value is discarded.
   * @param rowEntry
   * @returns The estimated row height value. If `null` or `undefined` then the default row height, based on the density, is applied.
   */
  getEstimatedRowHeight?: (rowEntry: RowEntry) => number | null;
  /**
   * Function that allows to specify the spacing between rows.
   * @param rowEntry
   * @param visibility With all properties from [[RowVisibilityParams]].
   * @returns The row spacing values.
   */
  getRowSpacing?: (rowEntry: RowEntry, visibility: RowVisibilityParams) => RowSpacing;
  /** Update the row height values before they're used.
   * Used to add detail panel heights.
   * @param entry
   * @param rowEntry
   */
  applyRowHeight?: (entry: HeightEntry, rowEntry: RowEntry) => void;
  virtualizeColumnsWithAutoRowHeight?: boolean;
  resizeThrottleMs: number;
  onResize?: (lastSize: Size) => void;
  onWheel?: (event: React.WheelEvent) => void;
  onTouchMove?: (event: React.TouchEvent) => void;
  onRenderContextChange?: (c: RenderContext) => void;
  onScrollChange?: (scrollPosition: {
    top: number;
    left: number;
  }, nextRenderContext: RenderContext) => void;
  focusedVirtualCell: () => FocusedCell | null;
  rowBufferPx: number;
  columnBufferPx: number;
  scrollReset?: any;
  getColspan: (rowId: RowId, column: ColumnWithWidth, columnIndex: integer) => number;
  renderRow: (params: {
    id: any;
    model: Row;
    rowIndex: number;
    offsetLeft: number;
    columnsTotalWidth: number;
    baseRowHeight: number | 'auto';
    columns: ColumnWithWidth[];
    firstColumnIndex: number;
    lastColumnIndex: number;
    focusedColumnIndex: number | undefined;
    isFirstVisible: boolean;
    isLastVisible: boolean;
    isVirtualFocusRow: boolean;
    showBottomBorder: boolean;
  }) => React.ReactElement;
  renderInfiniteLoadingTrigger: (id: any) => React.ReactElement;
};
export declare const useVirtualizer: (params: VirtualizerParams) => {
  store: Store<Dimensions.State & Virtualization.State & Colspan.State & Rowspan.State>;
  api: {
    updateDimensions: () => void;
    debouncedUpdateDimensions: ((() => void) & import("@mui/x-internals/throttle/throttle").Cancelable) | undefined;
    rowsMeta: {
      getRowHeight: (rowId: RowId) => any;
      setLastMeasuredRowIndex: (index: number) => void;
      storeRowHeightMeasurement: (id: RowId, height: number) => void;
      hydrateRowsMeta: () => void;
      observeRowHeight: (element: Element, rowId: RowId) => () => void | undefined;
      rowHasAutoHeight: (id: RowId) => any;
      getRowHeightEntry: (rowId: RowId) => any;
      getLastMeasuredRowIndex: () => number;
      resetRowHeights: () => void;
    };
  } & {
    getters: {
      setPanels: React.Dispatch<React.SetStateAction<Readonly<Map<any, React.ReactNode>>>>;
      getRows: (rowParams: {
        rows?: RowEntry[];
        position?: import("./models/index.js").PinnedRowPosition;
        renderContext?: RenderContext;
      } | undefined, unstable_rowTree: Record<RowId, any>) => React.ReactNode[];
      getContainerProps: () => {
        ref: React.RefObject<HTMLDivElement | null>;
      };
      getScrollerProps: () => {
        ref: React.RefObject<HTMLDivElement | null>;
        onScroll: () => void;
        onWheel: ((event: React.WheelEvent) => void) | undefined;
        onTouchMove: ((event: React.TouchEvent) => void) | undefined;
        style: React.CSSProperties;
        role: string;
        tabIndex: number | undefined;
      };
      getContentProps: () => {
        style: React.CSSProperties;
        role: string;
        ref: (node: HTMLDivElement | null) => void;
      };
      getRenderZoneProps: () => {
        role: string;
      };
      getScrollbarVerticalProps: () => {
        ref: React.RefObject<HTMLDivElement | null>;
        scrollPosition: React.RefObject<{
          top: number;
          left: number;
        }>;
      };
      getScrollbarHorizontalProps: () => {
        ref: React.RefObject<HTMLDivElement | null>;
        scrollPosition: React.RefObject<{
          top: number;
          left: number;
        }>;
      };
      getScrollAreaProps: () => {
        scrollPosition: React.RefObject<{
          top: number;
          left: number;
        }>;
      };
    };
    useVirtualization: () => BaseState;
    setPanels: React.Dispatch<React.SetStateAction<Readonly<Map<any, React.ReactNode>>>>;
    forceUpdateRenderContext: () => void;
    getCellColSpanInfo: (rowId: RowId, columnIndex: integer) => import("./models/index.js").CellColSpanInfo;
    calculateColSpan: (rowId: RowId, minFirstColumn: integer, maxLastColumn: integer, columns: ColumnWithWidth[]) => void;
    getHiddenCellsOrigin: () => Record<RowId, Record<number, number>>;
  } & {
    resetColSpan: () => void;
    getCellColSpanInfo: (rowId: RowId, columnIndex: integer) => import("./models/index.js").CellColSpanInfo | undefined;
    calculateColSpan: (rowId: RowId, minFirstColumn: integer, maxLastColumn: integer, columns: ColumnWithWidth[]) => void;
  } & {
    getHiddenCellsOrigin: () => Record<number, Record<number, number>>;
  } & {
    getViewportPageSize: () => number;
  };
};