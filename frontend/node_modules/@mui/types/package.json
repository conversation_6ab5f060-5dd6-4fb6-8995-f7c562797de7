{"name": "@mui/types", "version": "7.4.4", "author": "MUI Team", "description": "Utility types for Material UI.", "types": "./index.d.ts", "keywords": ["react", "react-component", "mui", "types"], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/mui-types"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://github.com/mui/material-ui/tree/master/packages/mui-types", "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "dependencies": {"@babel/runtime": "^7.27.6"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "private": false, "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}}