version: '3.8'

# Development override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # PostgreSQL Database (same as production)
  postgres:
    ports:
      - "5432:5432"

  # Redis (same as production)
  redis:
    ports:
      - "6379:6379"

  # API with development settings
  api:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-scrapy_user}:${POSTGRES_PASSWORD:-scrapy_password}@postgres:5432/${POSTGRES_DB:-scrapy_db}
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - SCRAPY_PROJECT_PATH=/app/ecommerce_scraper
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./logs:/app/logs
    command: >
      sh -c "
        echo 'Development mode - setting up database...' &&
        python scripts/setup_database.py &&
        echo 'Starting API server with auto-reload...' &&
        uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # Celery Worker with development settings
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-scrapy_user}:${POSTGRES_PASSWORD:-scrapy_password}@postgres:5432/${POSTGRES_DB:-scrapy_db}
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - SCRAPY_PROJECT_PATH=/app/ecommerce_scraper
    volumes:
      - .:/app
      - ./logs:/app/logs
    command: celery -A api.celery_app worker --loglevel=debug --concurrency=2

  # Celery Beat with development settings
  beat:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-scrapy_user}:${POSTGRES_PASSWORD:-scrapy_password}@postgres:5432/${POSTGRES_DB:-scrapy_db}
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - SCRAPING_SCHEDULE_HOUR=${SCRAPING_SCHEDULE_HOUR:-10}
      - SCRAPING_SCHEDULE_MINUTE=${SCRAPING_SCHEDULE_MINUTE:-0}
    volumes:
      - .:/app
      - ./logs:/app/logs
    command: celery -A api.celery_app beat --loglevel=debug

  # React development server
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: scrapy_frontend_dev
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    stdin_open: true
    tty: true
    command: npm start
