# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import hashlib
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Set

# Add the parent directory to the path to import database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from itemadapter import ItemAdapter
from scrapy.exceptions import DropItem

try:
    from database.connection import get_db_session
    from database.models import ScrapingSite, Product
    from database.crud import ProductCRUD, ScrapingSiteCRUD
except ImportError:
    # Fallback for when database modules are not available
    get_db_session = None
    Product = None
    ProductCRUD = None
    ScrapingSiteCRUD = None

logger = logging.getLogger(__name__)


class ValidationPipeline:
    """Pipeline to validate scraped items."""

    def process_item(self, item, spider):
        adapter = ItemAdapter(item)

        # Required fields validation
        required_fields = ['name', 'url', 'site_name']
        for field in required_fields:
            if not adapter.get(field):
                raise DropItem(f"Missing required field: {field} in {item}")

        # Clean and validate price
        if adapter.get('price'):
            try:
                price_str = str(adapter['price'])
                # Remove currency symbols and clean
                import re
                cleaned_price = re.sub(r'[^\d.,]', '', price_str)
                if ',' in cleaned_price and '.' in cleaned_price:
                    cleaned_price = cleaned_price.replace('.', '').replace(',', '.')
                elif ',' in cleaned_price:
                    cleaned_price = cleaned_price.replace(',', '.')

                adapter['price'] = float(cleaned_price) if cleaned_price else None
            except (ValueError, TypeError):
                adapter['price'] = None

        # Validate and clean original price
        if adapter.get('original_price'):
            try:
                original_price_str = str(adapter['original_price'])
                import re
                cleaned_original = re.sub(r'[^\d.,]', '', original_price_str)
                if ',' in cleaned_original and '.' in cleaned_original:
                    cleaned_original = cleaned_original.replace('.', '').replace(',', '.')
                elif ',' in cleaned_original:
                    cleaned_original = cleaned_original.replace(',', '.')

                adapter['original_price'] = float(cleaned_original) if cleaned_original else None
            except (ValueError, TypeError):
                adapter['original_price'] = None

        # Ensure external_id exists
        if not adapter.get('external_id'):
            # Generate from URL or other unique identifier
            url = adapter.get('url', '')
            adapter['external_id'] = str(hash(url))

        # Set default currency
        if not adapter.get('currency'):
            adapter['currency'] = 'ARS'

        # Validate image URLs
        if adapter.get('image_urls'):
            valid_images = []
            for img_url in adapter['image_urls']:
                if img_url and img_url.startswith(('http://', 'https://')):
                    valid_images.append(img_url)
            adapter['image_urls'] = valid_images

            if valid_images and not adapter.get('main_image_url'):
                adapter['main_image_url'] = valid_images[0]

        return item


class DeduplicationPipeline:
    """Pipeline to detect and handle duplicate items."""

    def __init__(self):
        self.seen_items: Set[str] = set()

    def process_item(self, item, spider):
        adapter = ItemAdapter(item)

        # Create a hash signature for the item
        signature_data = {
            'site_name': adapter.get('site_name', ''),
            'external_id': adapter.get('external_id', ''),
            'url': adapter.get('url', ''),
        }

        signature_string = '|'.join(str(v) for v in signature_data.values())
        item_hash = hashlib.md5(signature_string.encode()).hexdigest()

        if item_hash in self.seen_items:
            logger.debug(f"Duplicate item detected: {adapter.get('name', 'Unknown')}")
            raise DropItem(f"Duplicate item: {item}")

        self.seen_items.add(item_hash)
        adapter['hash_signature'] = item_hash

        return item


class DatabasePipeline:
    """Pipeline to save items to the database."""

    def __init__(self):
        self.db_session = None
        self.site_cache: Dict[str, int] = {}

    def open_spider(self, spider):
        """Initialize database connection when spider opens."""
        if get_db_session is None:
            logger.warning("Database modules not available. Items will not be saved to database.")
            return

        try:
            self.db_session = get_db_session()
            logger.info("Database connection established")

            # Cache site information
            self._cache_sites()

        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            self.db_session = None

    def close_spider(self, spider):
        """Close database connection when spider closes."""
        if self.db_session:
            self.db_session.close()
            logger.info("Database connection closed")

    def _cache_sites(self):
        """Cache site information for faster lookups."""
        if not self.db_session:
            return

        try:
            sites = ScrapingSiteCRUD.get_all(self.db_session)
            for site in sites:
                self.site_cache[site.name] = site.id
        except Exception as e:
            logger.error(f"Failed to cache sites: {e}")

    def _get_or_create_site(self, site_name: str, spider_name: str) -> int:
        """Get or create a scraping site record."""
        if site_name in self.site_cache:
            return self.site_cache[site_name]

        try:
            # Try to find existing site
            site = self.db_session.query(ScrapingSite).filter(
                ScrapingSite.name == site_name
            ).first()

            if not site:
                # Create new site
                site = ScrapingSiteCRUD.create(
                    self.db_session,
                    name=site_name,
                    base_url=f"https://{spider_name.replace('_', '.')}.com",
                    spider_name=spider_name
                )
                logger.info(f"Created new site record: {site_name}")

            self.site_cache[site_name] = site.id
            return site.id

        except Exception as e:
            logger.error(f"Failed to get or create site {site_name}: {e}")
            return None

    def process_item(self, item, spider):
        if not self.db_session:
            logger.warning("No database connection. Skipping item save.")
            return item

        adapter = ItemAdapter(item)

        try:
            # Get or create site
            site_name = adapter.get('site_name', spider.name)
            site_id = self._get_or_create_site(site_name, spider.name)

            if not site_id:
                logger.error(f"Could not determine site_id for item: {adapter.get('name')}")
                return item

            # Prepare product data
            product_data = {
                'external_id': adapter.get('external_id'),
                'url': adapter.get('url'),
                'name': adapter.get('name'),
                'description': adapter.get('description'),
                'category': adapter.get('category'),
                'brand': adapter.get('brand'),
                'price': adapter.get('price'),
                'original_price': adapter.get('original_price'),
                'currency': adapter.get('currency', 'ARS'),
                'in_stock': adapter.get('in_stock'),
                'stock_quantity': adapter.get('stock_quantity'),
                'availability_text': adapter.get('availability_text'),
                'image_urls': adapter.get('image_urls', []),
                'main_image_url': adapter.get('main_image_url'),
                'attributes': adapter.get('attributes', {}),
                'rating': adapter.get('rating'),
                'review_count': adapter.get('review_count'),
                'hash_signature': adapter.get('hash_signature'),
                'sku': adapter.get('sku'),
            }

            # Remove None values
            product_data = {k: v for k, v in product_data.items() if v is not None}

            # Create or update product
            product = ProductCRUD.create_or_update(
                self.db_session,
                site_id=site_id,
                external_id=adapter.get('external_id'),
                **product_data
            )

            logger.debug(f"Saved product: {product.name} (ID: {product.id})")

        except Exception as e:
            logger.error(f"Failed to save item to database: {e}")
            # Don't drop the item, just log the error

        return item


class EcommerceScraperPipeline:
    """Legacy pipeline for backward compatibility."""
    def process_item(self, item, spider):
        return item
