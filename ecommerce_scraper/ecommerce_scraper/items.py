# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy
from itemloaders.processors import TakeFirst, MapCompose, Join
from w3lib.html import remove_tags


def clean_text(value):
    """Clean text by removing extra whitespace and HTML tags."""
    if value:
        return remove_tags(value).strip()
    return value


def clean_price(value):
    """Clean price string and convert to float."""
    if value:
        # Remove currency symbols and non-numeric characters except dots and commas
        import re
        cleaned = re.sub(r'[^\d.,]', '', str(value))
        # Handle comma as decimal separator (common in Argentina)
        if ',' in cleaned and '.' in cleaned:
            # If both comma and dot, assume dot is thousands separator
            cleaned = cleaned.replace('.', '').replace(',', '.')
        elif ',' in cleaned:
            # Only comma, assume it's decimal separator
            cleaned = cleaned.replace(',', '.')

        try:
            return float(cleaned)
        except ValueError:
            return None
    return None


class ProductItem(scrapy.Item):
    """Item for scraped product data."""

    # Identifiers
    sku = scrapy.Field(output_processor=TakeFirst())
    external_id = scrapy.Field(output_processor=TakeFirst())
    url = scrapy.Field(output_processor=TakeFirst())

    # Basic information
    name = scrapy.Field(
        input_processor=MapCompose(clean_text),
        output_processor=TakeFirst()
    )
    description = scrapy.Field(
        input_processor=MapCompose(clean_text),
        output_processor=Join('\n')
    )
    category = scrapy.Field(
        input_processor=MapCompose(clean_text),
        output_processor=TakeFirst()
    )
    brand = scrapy.Field(
        input_processor=MapCompose(clean_text),
        output_processor=TakeFirst()
    )

    # Pricing
    price = scrapy.Field(
        input_processor=MapCompose(clean_price),
        output_processor=TakeFirst()
    )
    original_price = scrapy.Field(
        input_processor=MapCompose(clean_price),
        output_processor=TakeFirst()
    )
    currency = scrapy.Field(output_processor=TakeFirst())
    discount_percentage = scrapy.Field(output_processor=TakeFirst())

    # Availability
    in_stock = scrapy.Field(output_processor=TakeFirst())
    stock_quantity = scrapy.Field(output_processor=TakeFirst())
    availability_text = scrapy.Field(
        input_processor=MapCompose(clean_text),
        output_processor=TakeFirst()
    )

    # Images
    image_urls = scrapy.Field()
    main_image_url = scrapy.Field(output_processor=TakeFirst())

    # Additional data
    attributes = scrapy.Field()
    rating = scrapy.Field(output_processor=TakeFirst())
    review_count = scrapy.Field(output_processor=TakeFirst())

    # Metadata
    site_name = scrapy.Field(output_processor=TakeFirst())
    scraped_at = scrapy.Field(output_processor=TakeFirst())


class EcommerceScraperItem(ProductItem):
    """Legacy item class for backward compatibility."""
    pass
