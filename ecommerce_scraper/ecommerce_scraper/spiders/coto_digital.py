import scrapy
from datetime import datetime
from scrapy_playwright.page import PageMethod

from ..items import ProductItem


class CotoDigitalSpider(scrapy.Spider):
    name = "coto_digital"
    allowed_domains = ["www.cotodigital.com.ar"]
    start_urls = ["https://www.cotodigital.com.ar/sitios/cdigi/nuevositio"]

    # Custom settings for this spider
    custom_settings = {
        'DOWNLOAD_DELAY': 3,
        'RANDOMIZE_DOWNLOAD_DELAY': 1.0,
        'CONCURRENT_REQUESTS': 4,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
        'PLAYWRIGHT_BROWSER_TYPE': 'chromium',
        'PLAYWRIGHT_LAUNCH_OPTIONS': {
            'headless': True,
            'timeout': 60000,
        },
        'DOWNLOAD_HANDLERS': {
            'http': 'scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler',
            'https': 'scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler',
        },
        'TWISTED_REACTOR': 'twisted.internet.asyncioreactor.AsyncioSelectorReactor',
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.scraped_products = set()  # Track scraped products to avoid duplicates

    def start_requests(self):
        """Generate initial requests with Playwright."""
        self.logger.info("Starting Coto Digital spider...")

        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                callback=self.parse_homepage,
                meta={
                    'playwright': True,
                    'playwright_include_page': True,
                    'playwright_page_methods': [
                        PageMethod('wait_for_load_state', 'networkidle'),
                        PageMethod('wait_for_timeout', 5000),
                    ]
                }
            )

    async def parse_homepage(self, response):
        """Parse the homepage and generate product URLs to scrape."""
        page = response.meta['playwright_page']

        self.logger.info(f"Parsing homepage: {response.url}")
        self.logger.info(f"Page title: {await page.title()}")

        # Since Coto Digital is a SPA with dynamic content, we'll use a different approach
        # Instead of trying to find category links, we'll generate product URLs directly
        # based on the URL pattern we discovered: /sitios/cdigi/producto/{id}

        # Look for any category links for reference
        category_links = response.css('a[href*="/categoria/"]')
        self.logger.info(f"Found {len(category_links)} category links on homepage")

        # Log some category examples for debugging
        for i, link in enumerate(category_links[:5]):
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            self.logger.info(f"Category {i+1}: {text} -> {href}")

        # Try to find any actual product links on the homepage
        self.logger.info("Looking for actual product links on homepage...")

        # Look for any links that might lead to products
        all_links = await page.query_selector_all('a[href]')
        product_like_urls = set()

        for link in all_links[:200]:  # Check first 200 links
            try:
                href = await link.get_attribute('href')
                if href and any(word in href.lower() for word in ['producto', 'product', 'item']):
                    if href.startswith('/'):
                        href = f"https://www.cotodigital.com.ar{href}"
                    elif not href.startswith('http'):
                        href = f"https://www.cotodigital.com.ar/sitios/cdigi/{href}"
                    product_like_urls.add(href)
            except:
                continue

        self.logger.info(f"Found {len(product_like_urls)} potential product URLs on homepage")

        # If we found actual product URLs, use those first
        for url in list(product_like_urls)[:10]:  # Limit to first 10
            yield scrapy.Request(
                url=url,
                callback=self.parse_product,
                meta={
                    'playwright': True,
                    'playwright_include_page': True,
                    'playwright_page_methods': [
                        PageMethod('wait_for_load_state', 'networkidle'),
                        PageMethod('wait_for_timeout', 2000),
                    ],
                    'product_id': 'homepage_link'
                },
                dont_filter=True
            )

        await page.close()

        # Generate product URLs to test
        # We'll start with a range of product IDs to find valid products
        self.logger.info("Generating product URLs to test...")

        # Test a wider range of product IDs with different patterns
        # Many e-commerce sites use larger ID numbers
        product_ids = []

        # Test some common ID ranges
        product_ids.extend(range(1, 21))        # 1-20
        product_ids.extend(range(100, 121))     # 100-120
        product_ids.extend(range(1000, 1021))  # 1000-1020
        product_ids.extend(range(10000, 10021)) # 10000-10020

        # Also test some random larger numbers that might be valid
        import random
        random.seed(42)  # For reproducible results
        for _ in range(20):
            product_ids.append(random.randint(50000, 999999))

        self.logger.info(f"Testing {len(product_ids)} product IDs...")

        for product_id in product_ids:
            product_url = f"https://www.cotodigital.com.ar/sitios/cdigi/producto/{product_id}"
            yield scrapy.Request(
                url=product_url,
                callback=self.parse_product,
                meta={
                    'playwright': True,
                    'playwright_include_page': True,
                    'playwright_page_methods': [
                        PageMethod('wait_for_load_state', 'networkidle'),
                        PageMethod('wait_for_timeout', 2000),  # Reduced timeout
                    ],
                    'product_id': product_id
                },
                dont_filter=True  # Allow duplicate URLs
            )



    async def parse_product(self, response):
        """Parse individual product pages."""
        page = response.meta['playwright_page']
        product_id = response.meta.get('product_id', 'unknown')

        self.logger.info(f"Parsing product page: {response.url}")

        try:
            # Check if this is a valid product page
            page_title = await page.title()
            self.logger.debug(f"Product {product_id} page title: {page_title}")

            # Check for error indicators
            error_indicators = [
                "404", "not found", "no encontrado", "error",
                "página no encontrada", "producto no encontrado"
            ]

            if any(indicator in page_title.lower() for indicator in error_indicators):
                self.logger.debug(f"Product {product_id} appears to be an error page")
                await page.close()
                return

            # If the page title is just the generic site title, this might not be a valid product
            if page_title == "Coto Digital: Tu super a un click":
                self.logger.debug(f"Product {product_id} appears to be invalid (generic title)")
                await page.close()
                return

            # Look for product-specific content with more selectors
            product_selectors = [
                'h1', '.product-name', '.product-title', '[class*="product"]',
                '.item-title', '.product-info', '[data-testid*="product"]',
                '.product-detail', '.item-detail'
            ]

            product_content = None
            for selector in product_selectors:
                product_content = await page.query_selector(selector)
                if product_content:
                    break

            if not product_content:
                self.logger.debug(f"Product {product_id} has no product content")
                await page.close()
                return

            # Initialize product item
            item = ProductItem()

            # Basic information
            item['url'] = response.url
            item['site_name'] = 'Coto Digital'
            item['category'] = 'General'  # Default category
            item['scraped_at'] = datetime.now().isoformat()
            item['external_id'] = str(product_id)

            self.logger.info(f"Processing valid product {product_id}")

            # Product name - try multiple selectors
            name_selectors = [
                'h1.product-title',
                'h1.product-name',
                '.product-detail h1',
                '.product-info h1',
                'h1',
                '.title',
                '.product-title',
                '[class*="product-name"]',
                '[class*="title"]'
            ]

            product_name = None
            for selector in name_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        name = await element.text_content()
                        if name and name.strip() and len(name.strip()) > 2:
                            product_name = name.strip()
                            self.logger.debug(f"Found product name with selector '{selector}': {product_name}")
                            break
                except Exception as e:
                    self.logger.debug(f"Error with name selector '{selector}': {e}")

            if product_name:
                item['name'] = product_name
            else:
                # Fallback: use page title if no product name found
                item['name'] = f"Product {product_id}"
                self.logger.warning(f"No product name found for {product_id}, using fallback")

            # Price - try multiple selectors
            price_selectors = [
                '.price-current',
                '.current-price',
                '.price',
                '[class*="price"]:not([class*="original"]):not([class*="old"])',
                '.product-price',
                '[class*="precio"]'
            ]

            product_price = None
            for selector in price_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        price_text = await element.text_content()
                        if price_text and '$' in price_text:
                            product_price = price_text.strip()
                            self.logger.debug(f"Found price with selector '{selector}': {product_price}")
                            break
                except Exception as e:
                    self.logger.debug(f"Error with price selector '{selector}': {e}")

            if product_price:
                item['price'] = product_price

            # Currency
            item['currency'] = 'ARS'  # Default for Argentina

            # Description - try to find any descriptive text
            description_selectors = [
                '.product-description',
                '.description',
                '.product-detail .description',
                '.product-info .description',
                '[class*="description"]',
                '[class*="detail"]'
            ]

            descriptions = []
            for selector in description_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        desc_text = await element.text_content()
                        if desc_text and desc_text.strip():
                            descriptions.append(desc_text.strip())
                except Exception as e:
                    self.logger.debug(f"Error with description selector '{selector}': {e}")

            if descriptions:
                item['description'] = ' '.join(descriptions[:3])  # Limit to first 3 descriptions

            # Images
            image_selectors = [
                '.product-image img',
                '.product-gallery img',
                '.product-photos img',
                'img[class*="product"]',
                'img[alt*="product"]',
                'img[src*="product"]'
            ]

            images = []
            for selector in image_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        src = await element.get_attribute('src')
                        if src and src.startswith('http'):
                            images.append(src)
                except Exception as e:
                    self.logger.debug(f"Error with image selector '{selector}': {e}")

            if images:
                item['image_url'] = images[0]  # Use first image as main image

            # Availability - assume in stock if we found a price
            if product_price:
                item['availability'] = 'in_stock'
            else:
                item['availability'] = 'unknown'

            # Add to scraped products set to avoid duplicates
            if product_id not in self.scraped_products:
                self.scraped_products.add(product_id)
                self.logger.info(f"Successfully scraped product {product_id}: {item.get('name', 'Unknown')}")
                yield item
            else:
                self.logger.debug(f"Product {product_id} already scraped, skipping")

        except Exception as e:
            self.logger.error(f"Error parsing product {product_id}: {e}")
        finally:
            await page.close()


