import scrapy
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse
from scrapy_playwright.page import PageMethod

from ..items import ProductItem


class CotoDigitalSpider(scrapy.Spider):
    name = "coto_digital"
    allowed_domains = ["www.cotodigital.com.ar"]
    start_urls = ["https://www.cotodigital.com.ar/sitios/cdigi/nuevositio"]

    # Custom settings for this spider
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
        'CONCURRENT_REQUESTS': 8,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 4,
        'PLAYWRIGHT_BROWSER_TYPE': 'chromium',
        'PLAYWRIGHT_LAUNCH_OPTIONS': {
            'headless': True,
            'timeout': 30000,
        },
        'DOWNLOAD_HANDLERS': {
            'http': 'scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler',
            'https': 'scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler',
        },
        'TWISTED_REACTOR': 'twisted.internet.asyncioreactor.AsyncioSelectorReactor',
    }

    def start_requests(self):
        """Generate initial requests with Playwright."""
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                callback=self.parse_homepage,
                meta={
                    'playwright': True,
                    'playwright_include_page': True,
                    'playwright_page_methods': [
                        PageMethod('wait_for_load_state', 'networkidle'),
                        PageMethod('wait_for_timeout', 3000),
                    ]
                }
            )

    async def parse_homepage(self, response):
        """Parse the homepage to find category links."""
        page = response.meta['playwright_page']

        # Look for category navigation
        category_links = response.css('nav a[href*="categoria"], .category-menu a, .nav-category a')

        if not category_links:
            # Try alternative selectors for category links
            category_links = response.css('a[href*="/categoria/"], a[href*="/cat/"], .menu-item a')

        # Extract category URLs
        for link in category_links[:10]:  # Limit to first 10 categories for testing
            category_url = link.css('::attr(href)').get()
            if category_url:
                category_url = urljoin(response.url, category_url)
                category_name = link.css('::text').get()

                yield scrapy.Request(
                    url=category_url,
                    callback=self.parse_category,
                    meta={
                        'playwright': True,
                        'playwright_include_page': True,
                        'playwright_page_methods': [
                            PageMethod('wait_for_load_state', 'networkidle'),
                            PageMethod('wait_for_timeout', 2000),
                        ],
                        'category_name': category_name
                    }
                )

        # If no categories found, try to find product links directly
        if not category_links:
            product_links = response.css('a[href*="/producto/"], a[href*="/product/"], .product-link')
            for link in product_links[:5]:  # Limit for testing
                product_url = link.css('::attr(href)').get()
                if product_url:
                    product_url = urljoin(response.url, product_url)
                    yield scrapy.Request(
                        url=product_url,
                        callback=self.parse_product,
                        meta={
                            'playwright': True,
                            'playwright_include_page': True,
                            'playwright_page_methods': [
                                PageMethod('wait_for_load_state', 'networkidle'),
                            ]
                        }
                    )

        await page.close()

    async def parse_category(self, response):
        """Parse category pages to find product links."""
        page = response.meta['playwright_page']
        category_name = response.meta.get('category_name', 'Unknown')

        # Look for product links in the category page
        product_selectors = [
            'a[href*="/producto/"]',
            'a[href*="/product/"]',
            '.product-item a',
            '.product-card a',
            '.item-product a',
            'article a',
            '.product a'
        ]

        product_links = []
        for selector in product_selectors:
            links = response.css(selector)
            if links:
                product_links = links
                break

        # Extract product URLs
        for link in product_links[:20]:  # Limit products per category
            product_url = link.css('::attr(href)').get()
            if product_url:
                product_url = urljoin(response.url, product_url)
                yield scrapy.Request(
                    url=product_url,
                    callback=self.parse_product,
                    meta={
                        'playwright': True,
                        'playwright_include_page': True,
                        'playwright_page_methods': [
                            PageMethod('wait_for_load_state', 'networkidle'),
                        ],
                        'category_name': category_name
                    }
                )

        # Look for pagination
        next_page_selectors = [
            '.pagination .next',
            '.pager .next',
            'a[aria-label="Next"]',
            '.pagination a:contains("Siguiente")',
            '.pagination a:contains(">")'
        ]

        for selector in next_page_selectors:
            next_page = response.css(selector + '::attr(href)').get()
            if next_page:
                next_page_url = urljoin(response.url, next_page)
                yield scrapy.Request(
                    url=next_page_url,
                    callback=self.parse_category,
                    meta={
                        'playwright': True,
                        'playwright_include_page': True,
                        'playwright_page_methods': [
                            PageMethod('wait_for_load_state', 'networkidle'),
                            PageMethod('wait_for_timeout', 2000),
                        ],
                        'category_name': category_name
                    }
                )
                break

        await page.close()

    async def parse_product(self, response):
        """Parse individual product pages."""
        page = response.meta['playwright_page']
        category_name = response.meta.get('category_name', 'Unknown')

        # Initialize product item
        item = ProductItem()

        # Basic information
        item['url'] = response.url
        item['site_name'] = 'Coto Digital'
        item['category'] = category_name
        item['scraped_at'] = datetime.now().isoformat()

        # Product name - try multiple selectors
        name_selectors = [
            'h1.product-title',
            'h1.product-name',
            '.product-detail h1',
            '.product-info h1',
            'h1',
            '.title',
            '.product-title'
        ]

        for selector in name_selectors:
            name = response.css(selector + '::text').get()
            if name:
                item['name'] = name.strip()
                break

        # SKU/Product ID
        sku_selectors = [
            '[data-sku]::attr(data-sku)',
            '.product-sku::text',
            '.sku::text',
            '[class*="sku"]::text'
        ]

        for selector in sku_selectors:
            sku = response.css(selector).get()
            if sku:
                item['sku'] = sku.strip()
                break

        # External ID from URL
        url_parts = response.url.split('/')
        for part in reversed(url_parts):
            if part and part.isdigit():
                item['external_id'] = part
                break

        if not item.get('external_id'):
            # Try to extract from URL pattern
            import re
            match = re.search(r'/(\d+)(?:/|$)', response.url)
            if match:
                item['external_id'] = match.group(1)
            else:
                # Use URL hash as fallback
                item['external_id'] = str(hash(response.url))

        # Price - try multiple selectors
        price_selectors = [
            '.price-current::text',
            '.current-price::text',
            '.price::text',
            '[class*="price"]:not([class*="original"]):not([class*="old"])::text',
            '.product-price::text'
        ]

        for selector in price_selectors:
            price = response.css(selector).get()
            if price:
                item['price'] = price.strip()
                break

        # Original price (if on sale)
        original_price_selectors = [
            '.price-original::text',
            '.original-price::text',
            '.old-price::text',
            '[class*="price"][class*="original"]::text',
            '[class*="price"][class*="old"]::text'
        ]

        for selector in original_price_selectors:
            original_price = response.css(selector).get()
            if original_price:
                item['original_price'] = original_price.strip()
                break

        # Currency
        item['currency'] = 'ARS'  # Default for Argentina

        # Description
        description_selectors = [
            '.product-description::text',
            '.description::text',
            '.product-detail .description::text',
            '.product-info .description::text'
        ]

        descriptions = []
        for selector in description_selectors:
            desc_parts = response.css(selector).getall()
            if desc_parts:
                descriptions.extend(desc_parts)

        if descriptions:
            item['description'] = ' '.join([d.strip() for d in descriptions if d.strip()])

        # Brand
        brand_selectors = [
            '.brand::text',
            '.product-brand::text',
            '[data-brand]::attr(data-brand)',
            '.manufacturer::text'
        ]

        for selector in brand_selectors:
            brand = response.css(selector).get()
            if brand:
                item['brand'] = brand.strip()
                break

        # Images
        image_selectors = [
            '.product-image img::attr(src)',
            '.product-gallery img::attr(src)',
            '.product-photos img::attr(src)',
            'img[class*="product"]::attr(src)'
        ]

        images = []
        for selector in image_selectors:
            img_urls = response.css(selector).getall()
            for img_url in img_urls:
                if img_url:
                    full_img_url = urljoin(response.url, img_url)
                    images.append(full_img_url)

        if images:
            item['image_urls'] = images
            item['main_image_url'] = images[0]

        # Availability
        availability_selectors = [
            '.stock-status::text',
            '.availability::text',
            '[class*="stock"]::text',
            '.product-availability::text'
        ]

        for selector in availability_selectors:
            availability = response.css(selector).get()
            if availability:
                availability_text = availability.strip().lower()
                item['availability_text'] = availability
                item['in_stock'] = 'disponible' in availability_text or 'stock' in availability_text
                break

        # If no explicit availability, assume in stock if price exists
        if 'in_stock' not in item and item.get('price'):
            item['in_stock'] = True

        # Additional attributes
        attributes = {}

        # Try to extract product specifications
        spec_selectors = [
            '.product-specs tr',
            '.specifications tr',
            '.product-attributes tr'
        ]

        for selector in spec_selectors:
            specs = response.css(selector)
            for spec in specs:
                key = spec.css('td:first-child::text, th::text').get()
                value = spec.css('td:last-child::text').get()
                if key and value:
                    attributes[key.strip()] = value.strip()

        if attributes:
            item['attributes'] = attributes

        await page.close()

        yield item
