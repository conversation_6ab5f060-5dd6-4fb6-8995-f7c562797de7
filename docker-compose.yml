version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: scrapy_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-scrapy_db}
      POSTGRES_USER: ${POSTGRES_USER:-scrapy_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-scrapy_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-scrapy_user} -d ${POSTGRES_DB:-scrapy_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: scrapy_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Main API application
  api:
    build: .
    container_name: scrapy_api
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-scrapy_user}:${POSTGRES_PASSWORD:-scrapy_password}@postgres:5432/${POSTGRES_DB:-scrapy_db}
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - SCRAPY_PROJECT_PATH=/app/ecommerce_scraper
      - CONCURRENT_REQUESTS=${CONCURRENT_REQUESTS:-16}
      - CONCURRENT_REQUESTS_PER_DOMAIN=${CONCURRENT_REQUESTS_PER_DOMAIN:-8}
      - DOWNLOAD_DELAY=${DOWNLOAD_DELAY:-1}
      - RANDOMIZE_DOWNLOAD_DELAY=${RANDOMIZE_DOWNLOAD_DELAY:-0.5}
      - SCRAPING_SCHEDULE_HOUR=${SCRAPING_SCHEDULE_HOUR:-10}
      - SCRAPING_SCHEDULE_MINUTE=${SCRAPING_SCHEDULE_MINUTE:-0}
      - DATA_RETENTION_DAYS=${DATA_RETENTION_DAYS:-90}
      - PLAYWRIGHT_HEADLESS=true
      - PLAYWRIGHT_TIMEOUT=30000
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        sleep 10 &&
        echo 'Starting API server...' &&
        uvicorn api.main:app --host 0.0.0.0 --port 8000
      "

  # Celery Worker
  worker:
    build: .
    container_name: scrapy_worker
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-scrapy_user}:${POSTGRES_PASSWORD:-scrapy_password}@postgres:5432/${POSTGRES_DB:-scrapy_db}
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - SCRAPY_PROJECT_PATH=/app/ecommerce_scraper
      - CONCURRENT_REQUESTS=${CONCURRENT_REQUESTS:-16}
      - CONCURRENT_REQUESTS_PER_DOMAIN=${CONCURRENT_REQUESTS_PER_DOMAIN:-8}
      - DOWNLOAD_DELAY=${DOWNLOAD_DELAY:-1}
      - RANDOMIZE_DOWNLOAD_DELAY=${RANDOMIZE_DOWNLOAD_DELAY:-0.5}
      - DATA_RETENTION_DAYS=${DATA_RETENTION_DAYS:-90}
      - PLAYWRIGHT_HEADLESS=true
      - PLAYWRIGHT_TIMEOUT=30000
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A api.celery_app worker --loglevel=info --concurrency=4

  # Celery Beat (Scheduler)
  beat:
    build: .
    container_name: scrapy_beat
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-scrapy_user}:${POSTGRES_PASSWORD:-scrapy_password}@postgres:5432/${POSTGRES_DB:-scrapy_db}
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - SCRAPING_SCHEDULE_HOUR=${SCRAPING_SCHEDULE_HOUR:-10}
      - SCRAPING_SCHEDULE_MINUTE=${SCRAPING_SCHEDULE_MINUTE:-0}
    volumes:
      - ./logs:/app/logs
      - beat_data:/app/celerybeat-schedule
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A api.celery_app beat --loglevel=info

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: scrapy_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./frontend/build:/usr/share/nginx/html
    depends_on:
      - api
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  beat_data:
    driver: local

networks:
  default:
    name: scrapy_network
