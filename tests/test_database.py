"""
Tests for database models and CRUD operations.
"""
import pytest
from datetime import datetime

from database.models import ScrapingSite, Product, ScrapingJob, SiteStatus, JobStatus
from database.crud import ScrapingSiteCRUD, ProductCRUD, ScrapingJobCRUD


class TestScrapingSiteModel:
    """Test ScrapingSite model and CRUD operations."""
    
    def test_create_site(self, db_session, sample_site_data):
        """Test creating a new site."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        assert site.id is not None
        assert site.name == sample_site_data["name"]
        assert site.base_url == sample_site_data["base_url"]
        assert site.spider_name == sample_site_data["spider_name"]
        assert site.enabled == sample_site_data["enabled"]
        assert site.status == SiteStatus.ACTIVE  # Default status
        assert site.created_at is not None
        assert site.updated_at is not None
    
    def test_get_site_by_id(self, db_session, sample_site_data):
        """Test getting a site by ID."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        retrieved_site = ScrapingSiteCRUD.get_by_id(db_session, site.id)
        
        assert retrieved_site is not None
        assert retrieved_site.id == site.id
        assert retrieved_site.name == site.name
    
    def test_get_all_sites(self, db_session, sample_site_data):
        """Test getting all sites."""
        # Create multiple sites
        site1 = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        site2_data = {**sample_site_data, "name": "Site 2", "spider_name": "spider2"}
        site2 = ScrapingSiteCRUD.create(db_session, **site2_data)
        
        sites = ScrapingSiteCRUD.get_all(db_session)
        assert len(sites) >= 2
        site_ids = [s.id for s in sites]
        assert site1.id in site_ids
        assert site2.id in site_ids
    
    def test_get_active_sites(self, db_session, sample_site_data):
        """Test getting only active sites."""
        # Create active site
        active_site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        # Create inactive site
        inactive_data = {**sample_site_data, "name": "Inactive Site", "enabled": False}
        inactive_site = ScrapingSiteCRUD.create(db_session, **inactive_data)
        
        active_sites = ScrapingSiteCRUD.get_active(db_session)
        active_site_ids = [s.id for s in active_sites]
        
        assert active_site.id in active_site_ids
        assert inactive_site.id not in active_site_ids
    
    def test_update_site(self, db_session, sample_site_data):
        """Test updating a site."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        original_updated_at = site.updated_at
        
        # Update the site
        updated_site = ScrapingSiteCRUD.update(
            db_session, 
            site.id, 
            name="Updated Name",
            enabled=False
        )
        
        assert updated_site.name == "Updated Name"
        assert updated_site.enabled == False
        assert updated_site.updated_at > original_updated_at
    
    def test_delete_site(self, db_session, sample_site_data):
        """Test deleting a site."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        site_id = site.id
        
        # Delete the site
        success = ScrapingSiteCRUD.delete(db_session, site_id)
        assert success == True
        
        # Verify it's deleted
        deleted_site = ScrapingSiteCRUD.get_by_id(db_session, site_id)
        assert deleted_site is None


class TestProductModel:
    """Test Product model and CRUD operations."""
    
    def test_create_product(self, db_session, sample_site_data, sample_product_data):
        """Test creating a new product."""
        # Create a site first
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        # Create a product
        product = ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        assert product.id is not None
        assert product.site_id == site.id
        assert product.name == sample_product_data["name"]
        assert product.external_id == sample_product_data["external_id"]
        assert product.price == sample_product_data["price"]
        assert product.is_active == True
        assert product.first_seen_at is not None
        assert product.last_updated_at is not None
    
    def test_update_existing_product(self, db_session, sample_site_data, sample_product_data):
        """Test updating an existing product."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        product = ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        original_price = product.price
        
        # Update the product with new price
        updated_data = {**sample_product_data, "price": 149.99}
        updated_product = ProductCRUD.create_or_update(
            db_session,
            site_id=site.id,
            **updated_data
        )
        
        # Should be the same product (same ID) but with updated price
        assert updated_product.id == product.id
        assert updated_product.price == 149.99
        assert updated_product.price != original_price
    
    def test_get_products_by_site(self, db_session, sample_site_data, sample_product_data):
        """Test getting products for a specific site."""
        # Create two sites
        site1 = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        site2_data = {**sample_site_data, "name": "Site 2", "spider_name": "spider2"}
        site2 = ScrapingSiteCRUD.create(db_session, **site2_data)
        
        # Create products for each site
        product1 = ProductCRUD.create_or_update(
            db_session, 
            site_id=site1.id, 
            **sample_product_data
        )
        product2_data = {**sample_product_data, "external_id": "test-456", "name": "Product 2"}
        product2 = ProductCRUD.create_or_update(
            db_session, 
            site_id=site2.id, 
            **product2_data
        )
        
        # Get products for site1
        site1_products = ProductCRUD.get_by_site(db_session, site1.id)
        assert len(site1_products) == 1
        assert site1_products[0].id == product1.id
        
        # Get products for site2
        site2_products = ProductCRUD.get_by_site(db_session, site2.id)
        assert len(site2_products) == 1
        assert site2_products[0].id == product2.id
    
    def test_search_products(self, db_session, sample_site_data, sample_product_data):
        """Test searching products."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        product = ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        # Search by name
        results = ProductCRUD.search(db_session, "Test")
        assert len(results) >= 1
        assert product.id in [p.id for p in results]
        
        # Search by category
        results = ProductCRUD.search(db_session, "", category="Electronics")
        assert len(results) >= 1
        assert product.id in [p.id for p in results]


class TestScrapingJobModel:
    """Test ScrapingJob model and CRUD operations."""
    
    def test_create_job(self, db_session, sample_site_data):
        """Test creating a new job."""
        # Create a site first
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        # Create a job
        job = ScrapingJobCRUD.create(db_session, site_id=site.id)
        
        assert job.id is not None
        assert job.site_id == site.id
        assert job.status == JobStatus.PENDING
        assert job.items_scraped == 0
        assert job.items_saved == 0
        assert job.errors_count == 0
        assert job.created_at is not None
    
    def test_update_job_status(self, db_session, sample_site_data):
        """Test updating job status."""
        # Create a site and job
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        job = ScrapingJobCRUD.create(db_session, site_id=site.id)
        
        # Update to running
        updated_job = ScrapingJobCRUD.update_status(
            db_session, 
            job.id, 
            JobStatus.RUNNING,
            items_scraped=10,
            items_saved=8
        )
        
        assert updated_job.status == JobStatus.RUNNING
        assert updated_job.items_scraped == 10
        assert updated_job.items_saved == 8
        assert updated_job.started_at is not None
    
    def test_get_recent_jobs(self, db_session, sample_site_data):
        """Test getting recent jobs."""
        # Create a site and multiple jobs
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        job1 = ScrapingJobCRUD.create(db_session, site_id=site.id)
        job2 = ScrapingJobCRUD.create(db_session, site_id=site.id)
        
        recent_jobs = ScrapingJobCRUD.get_recent(db_session, limit=10)
        job_ids = [j.id for j in recent_jobs]
        
        assert job1.id in job_ids
        assert job2.id in job_ids
    
    def test_get_jobs_by_site(self, db_session, sample_site_data):
        """Test getting jobs for a specific site."""
        # Create two sites
        site1 = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        site2_data = {**sample_site_data, "name": "Site 2", "spider_name": "spider2"}
        site2 = ScrapingSiteCRUD.create(db_session, **site2_data)
        
        # Create jobs for each site
        job1 = ScrapingJobCRUD.create(db_session, site_id=site1.id)
        job2 = ScrapingJobCRUD.create(db_session, site_id=site2.id)
        
        # Get jobs for site1
        site1_jobs = ScrapingJobCRUD.get_by_site(db_session, site1.id)
        assert len(site1_jobs) == 1
        assert site1_jobs[0].id == job1.id
        
        # Get jobs for site2
        site2_jobs = ScrapingJobCRUD.get_by_site(db_session, site2.id)
        assert len(site2_jobs) == 1
        assert site2_jobs[0].id == job2.id
