"""
Tests for API endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON><PERSON>

from database.models import ScrapingSite, Product
from database.crud import ScrapingSiteCRUD, ProductCRUD


class TestSitesAPI:
    """Test sites API endpoints."""
    
    def test_create_site(self, client: TestClient, sample_site_data):
        """Test creating a new site."""
        response = client.post("/api/sites/", json=sample_site_data)
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == sample_site_data["name"]
        assert data["base_url"] == sample_site_data["base_url"]
        assert data["spider_name"] == sample_site_data["spider_name"]
    
    def test_get_sites(self, client: TestClient, db_session, sample_site_data):
        """Test getting all sites."""
        # Create a site first
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        response = client.get("/api/sites/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == sample_site_data["name"]
    
    def test_get_site_by_id(self, client: TestClient, db_session, sample_site_data):
        """Test getting a site by ID."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        response = client.get(f"/api/sites/{site.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == site.id
        assert data["name"] == sample_site_data["name"]
    
    def test_get_nonexistent_site(self, client: TestClient):
        """Test getting a non-existent site."""
        response = client.get("/api/sites/999")
        assert response.status_code == 404
    
    def test_update_site(self, client: TestClient, db_session, sample_site_data):
        """Test updating a site."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        update_data = {"name": "Updated Site Name"}
        response = client.put(f"/api/sites/{site.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Site Name"
    
    def test_delete_site(self, client: TestClient, db_session, sample_site_data):
        """Test deleting a site."""
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        response = client.delete(f"/api/sites/{site.id}")
        assert response.status_code == 200
        
        # Verify site is deleted
        response = client.get(f"/api/sites/{site.id}")
        assert response.status_code == 404


class TestProductsAPI:
    """Test products API endpoints."""
    
    def test_get_products(self, client: TestClient, db_session, sample_site_data, sample_product_data):
        """Test getting all products."""
        # Create a site first
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        # Create a product
        product = ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        response = client.get("/api/products/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        
        # Find our product in the response
        our_product = next((p for p in data if p["id"] == product.id), None)
        assert our_product is not None
        assert our_product["name"] == sample_product_data["name"]
    
    def test_search_products(self, client: TestClient, db_session, sample_site_data, sample_product_data):
        """Test searching products."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        product = ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        response = client.get("/api/products/search", params={"q": "Test"})
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        assert data[0]["name"] == sample_product_data["name"]
    
    def test_get_categories(self, client: TestClient, db_session, sample_site_data, sample_product_data):
        """Test getting product categories."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        response = client.get("/api/products/categories")
        assert response.status_code == 200
        data = response.json()
        assert sample_product_data["category"] in data
    
    def test_get_brands(self, client: TestClient, db_session, sample_site_data, sample_product_data):
        """Test getting product brands."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        response = client.get("/api/products/brands")
        assert response.status_code == 200
        data = response.json()
        assert sample_product_data["brand"] in data


class TestJobsAPI:
    """Test jobs API endpoints."""
    
    def test_get_jobs(self, client: TestClient):
        """Test getting all jobs."""
        response = client.get("/api/jobs/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_create_job(self, client: TestClient, db_session, sample_site_data):
        """Test creating a new job."""
        # Create a site first
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        
        # Note: This test might fail if Celery is not running
        # In a real test environment, you'd mock the Celery task
        job_data = {"site_id": site.id}
        response = client.post("/api/jobs/", json=job_data)
        
        # The response might be 500 if Celery is not available
        # In production tests, you'd have Celery running or mocked
        assert response.status_code in [201, 500]


class TestDashboardAPI:
    """Test dashboard API endpoints."""
    
    def test_get_dashboard_stats(self, client: TestClient):
        """Test getting dashboard statistics."""
        response = client.get("/api/dashboard/stats")
        assert response.status_code == 200
        data = response.json()
        assert "total_sites" in data
        assert "active_sites" in data
        assert "total_products" in data
        assert "jobs_24h" in data
        assert "recent_jobs" in data
    
    def test_get_health(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code in [200, 503]  # 503 if services are down
        data = response.json()
        assert "status" in data
        assert "database" in data


class TestExportAPI:
    """Test export API endpoints."""
    
    def test_export_csv(self, client: TestClient, db_session, sample_site_data, sample_product_data):
        """Test CSV export."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        response = client.get("/api/export/csv")
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
    
    def test_export_json(self, client: TestClient, db_session, sample_site_data, sample_product_data):
        """Test JSON export."""
        # Create a site and product
        site = ScrapingSiteCRUD.create(db_session, **sample_site_data)
        ProductCRUD.create_or_update(
            db_session, 
            site_id=site.id, 
            **sample_product_data
        )
        
        response = client.get("/api/export/json")
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
