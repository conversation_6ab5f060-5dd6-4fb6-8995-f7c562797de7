# Deployment Guide

This guide covers deploying the E-commerce Web Scraping Application in various environments.

## Prerequisites

- <PERSON>er and Docker Compose
- Git
- At least 4GB RAM and 20GB disk space
- PostgreSQL 15+ (if not using Docker)
- Redis 7+ (if not using Docker)
- Node.js 18+ (for frontend development)
- Python 3.11+ (for backend development)

## Quick Start with Docker

### 1. <PERSON><PERSON> and Configure

```bash
git clone <repository-url>
cd scrapy
cp .env.example .env
```

Edit `.env` file with your configuration:

```bash
# Database Configuration
POSTGRES_USER=scrapy_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=scrapy_db

# Redis Configuration (default is fine for Docker)
REDIS_URL=redis://redis:6379/0

# Scraping Configuration
SCRAPING_SCHEDULE_HOUR=10
SCRAPING_SCHEDULE_MINUTE=0
DOWNLOAD_DELAY=1
CONCURRENT_REQUESTS=16

# Data Retention
DATA_RETENTION_DAYS=90

# Optional: Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
NOTIFICATION_EMAIL=<EMAIL>
```

### 2. Deploy with Docker Compose

```bash
# Production deployment
./scripts/deploy.sh start

# Development deployment
./scripts/deploy.sh dev

# Production with Nginx
./scripts/deploy.sh prod
```

### 3. Access the Application

- **Web Interface**: http://localhost:3000 (dev) or http://localhost (prod)
- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## Manual Deployment

### 1. Database Setup

```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE scrapy_db;
CREATE USER scrapy_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE scrapy_db TO scrapy_user;
\q

# Install Redis
sudo apt-get install redis-server
```

### 2. Backend Setup

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install

# Set up database
python scripts/setup_database.py

# Run database migrations
alembic upgrade head
```

### 3. Start Services

```bash
# Start API server
uvicorn api.main:app --host 0.0.0.0 --port 8000

# Start Celery worker (in another terminal)
celery -A api.celery_app worker --loglevel=info

# Start Celery beat scheduler (in another terminal)
celery -A api.celery_app beat --loglevel=info
```

### 4. Frontend Setup

```bash
cd frontend
npm install
npm run build

# For development
npm start

# For production, serve the build folder with a web server
```

## Environment Variables

### Required Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://scrapy_user:scrapy_password@localhost:5432/scrapy_db` |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379/0` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `API_HOST` | API server host | `0.0.0.0` |
| `API_PORT` | API server port | `8000` |
| `DEBUG` | Enable debug mode | `false` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `CONCURRENT_REQUESTS` | Scrapy concurrent requests | `16` |
| `DOWNLOAD_DELAY` | Delay between requests (seconds) | `1` |
| `SCRAPING_SCHEDULE_HOUR` | Daily scraping hour (0-23) | `10` |
| `SCRAPING_SCHEDULE_MINUTE` | Daily scraping minute (0-59) | `0` |
| `DATA_RETENTION_DAYS` | Days to keep old data | `90` |
| `PLAYWRIGHT_HEADLESS` | Run browser in headless mode | `true` |
| `PLAYWRIGHT_TIMEOUT` | Browser timeout (ms) | `30000` |

## Production Considerations

### Security

1. **Change default passwords** in `.env` file
2. **Use HTTPS** in production (configure SSL certificates)
3. **Firewall configuration**: Only expose necessary ports
4. **Database security**: Use strong passwords and limit connections
5. **API security**: Consider adding authentication if needed

### Performance

1. **Resource allocation**:
   - Minimum 4GB RAM for full stack
   - 2 CPU cores recommended
   - SSD storage for better database performance

2. **Scaling**:
   - Increase Celery worker concurrency for more scraping capacity
   - Use multiple worker instances for high-volume scraping
   - Consider database connection pooling for high traffic

3. **Monitoring**:
   - Set up log aggregation (ELK stack, Grafana)
   - Monitor system resources (CPU, memory, disk)
   - Set up alerts for failed scraping jobs

### Backup and Recovery

1. **Database backups**:
   ```bash
   # Create backup
   pg_dump -h localhost -U scrapy_user scrapy_db > backup.sql
   
   # Restore backup
   psql -h localhost -U scrapy_user scrapy_db < backup.sql
   ```

2. **Configuration backups**:
   - Back up `.env` file
   - Back up Docker Compose configurations
   - Version control all configuration files

### Maintenance

1. **Regular updates**:
   - Update Docker images regularly
   - Monitor for security updates
   - Test updates in staging environment first

2. **Data cleanup**:
   - The system automatically cleans old data based on `DATA_RETENTION_DAYS`
   - Monitor disk usage and adjust retention as needed
   - Consider archiving old data instead of deletion

3. **Log rotation**:
   - Configure log rotation to prevent disk space issues
   - Monitor log files for errors and warnings

## Troubleshooting

### Common Issues

1. **Database connection errors**:
   - Check PostgreSQL is running
   - Verify connection string in `.env`
   - Check firewall settings

2. **Celery worker not starting**:
   - Check Redis connection
   - Verify Python dependencies are installed
   - Check for import errors in logs

3. **Scraping failures**:
   - Check target website availability
   - Verify Playwright browser installation
   - Review spider logs for errors

4. **Frontend not loading**:
   - Check API server is running
   - Verify CORS settings
   - Check browser console for errors

### Health Checks

Use the built-in health check endpoints:

```bash
# Overall system health
curl http://localhost:8000/health

# Detailed metrics
curl http://localhost:8000/api/dashboard/metrics

# Database health
curl http://localhost:8000/api/dashboard/health
```

### Logs

Check logs for debugging:

```bash
# Docker logs
docker-compose logs api
docker-compose logs worker
docker-compose logs beat

# Application logs (if using file logging)
tail -f logs/api.log
tail -f logs/celery.log
```

## Support

For issues and questions:

1. Check the logs for error messages
2. Review this documentation
3. Check the API documentation at `/docs`
4. Create an issue in the repository with:
   - Error messages
   - Steps to reproduce
   - Environment details
   - Log excerpts
