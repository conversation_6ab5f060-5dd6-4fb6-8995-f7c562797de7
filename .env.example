# Database Configuration
DATABASE_URL=postgresql://scrapy_user:scrapy_password@localhost:5432/scrapy_db
POSTGRES_USER=scrapy_user
POSTGRES_PASSWORD=scrapy_password
POSTGRES_DB=scrapy_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your-secret-key-here

# Scrapy Configuration
SCRAPY_PROJECT_PATH=/app/ecommerce_scraper
DOWNLOAD_DELAY=1
RANDOMIZE_DOWNLOAD_DELAY=0.5
CONCURRENT_REQUESTS=16
CONCURRENT_REQUESTS_PER_DOMAIN=8

# Scheduling Configuration
SCRAPING_SCHEDULE_HOUR=10
SCRAPING_SCHEDULE_MINUTE=0
TIMEZONE=America/Argentina/Buenos_Aires

# Data Retention
DATA_RETENTION_DAYS=90

# Email Notifications (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
NOTIFICATION_EMAIL=<EMAIL>

# Development
DEBUG=true
LOG_LEVEL=INFO
