"""rename_metadata_to_job_metadata

Revision ID: 2b2f9c5fdc4e
Revises: 
Create Date: 2025-08-01 16:51:37.288762

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2b2f9c5fdc4e'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scraping_sites',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('base_url', sa.String(length=500), nullable=False),
    sa.Column('spider_name', sa.String(length=100), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('enabled', sa.Boolean(), nullable=True),
    sa.Column('schedule_hour', sa.Integer(), nullable=True),
    sa.Column('schedule_minute', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_scraped_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraping_sites_id'), 'scraping_sites', ['id'], unique=False)
    op.create_table('products',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('site_id', sa.Integer(), nullable=False),
    sa.Column('sku', sa.String(length=255), nullable=True),
    sa.Column('external_id', sa.String(length=255), nullable=True),
    sa.Column('url', sa.String(length=1000), nullable=False),
    sa.Column('name', sa.String(length=500), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=255), nullable=True),
    sa.Column('brand', sa.String(length=255), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('original_price', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('discount_percentage', sa.Float(), nullable=True),
    sa.Column('in_stock', sa.Boolean(), nullable=True),
    sa.Column('stock_quantity', sa.Integer(), nullable=True),
    sa.Column('availability_text', sa.String(length=255), nullable=True),
    sa.Column('image_urls', sa.JSON(), nullable=True),
    sa.Column('main_image_url', sa.String(length=1000), nullable=True),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('rating', sa.Float(), nullable=True),
    sa.Column('review_count', sa.Integer(), nullable=True),
    sa.Column('first_seen_at', sa.DateTime(), nullable=True),
    sa.Column('last_updated_at', sa.DateTime(), nullable=True),
    sa.Column('scraped_at', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('hash_signature', sa.String(length=64), nullable=True),
    sa.ForeignKeyConstraint(['site_id'], ['scraping_sites.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('site_id', 'external_id', name='uq_site_external_id')
    )
    op.create_index('idx_product_category_brand', 'products', ['category', 'brand'], unique=False)
    op.create_index('idx_product_price_range', 'products', ['price'], unique=False)
    op.create_index('idx_product_scraped_at', 'products', ['scraped_at'], unique=False)
    op.create_index('idx_product_site_external_id', 'products', ['site_id', 'external_id'], unique=False)
    op.create_index('idx_product_site_sku', 'products', ['site_id', 'sku'], unique=False)
    op.create_index(op.f('ix_products_brand'), 'products', ['brand'], unique=False)
    op.create_index(op.f('ix_products_category'), 'products', ['category'], unique=False)
    op.create_index(op.f('ix_products_external_id'), 'products', ['external_id'], unique=False)
    op.create_index(op.f('ix_products_hash_signature'), 'products', ['hash_signature'], unique=False)
    op.create_index(op.f('ix_products_id'), 'products', ['id'], unique=False)
    op.create_index(op.f('ix_products_sku'), 'products', ['sku'], unique=False)
    op.create_table('scraping_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('site_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('celery_task_id', sa.String(length=255), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('items_scraped', sa.Integer(), nullable=True),
    sa.Column('items_saved', sa.Integer(), nullable=True),
    sa.Column('errors_count', sa.Integer(), nullable=True),
    sa.Column('log_messages', sa.JSON(), nullable=True),
    sa.Column('error_messages', sa.JSON(), nullable=True),
    sa.Column('job_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['site_id'], ['scraping_sites.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scraping_jobs_celery_task_id'), 'scraping_jobs', ['celery_task_id'], unique=False)
    op.create_index(op.f('ix_scraping_jobs_id'), 'scraping_jobs', ['id'], unique=False)
    op.create_table('product_price_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('original_price', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('discount_percentage', sa.Float(), nullable=True),
    sa.Column('in_stock', sa.Boolean(), nullable=True),
    sa.Column('stock_quantity', sa.Integer(), nullable=True),
    sa.Column('recorded_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_price_history_product_date', 'product_price_history', ['product_id', 'recorded_at'], unique=False)
    op.create_index(op.f('ix_product_price_history_id'), 'product_price_history', ['id'], unique=False)
    op.create_index(op.f('ix_product_price_history_recorded_at'), 'product_price_history', ['recorded_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_product_price_history_recorded_at'), table_name='product_price_history')
    op.drop_index(op.f('ix_product_price_history_id'), table_name='product_price_history')
    op.drop_index('idx_price_history_product_date', table_name='product_price_history')
    op.drop_table('product_price_history')
    op.drop_index(op.f('ix_scraping_jobs_id'), table_name='scraping_jobs')
    op.drop_index(op.f('ix_scraping_jobs_celery_task_id'), table_name='scraping_jobs')
    op.drop_table('scraping_jobs')
    op.drop_index(op.f('ix_products_sku'), table_name='products')
    op.drop_index(op.f('ix_products_id'), table_name='products')
    op.drop_index(op.f('ix_products_hash_signature'), table_name='products')
    op.drop_index(op.f('ix_products_external_id'), table_name='products')
    op.drop_index(op.f('ix_products_category'), table_name='products')
    op.drop_index(op.f('ix_products_brand'), table_name='products')
    op.drop_index('idx_product_site_sku', table_name='products')
    op.drop_index('idx_product_site_external_id', table_name='products')
    op.drop_index('idx_product_scraped_at', table_name='products')
    op.drop_index('idx_product_price_range', table_name='products')
    op.drop_index('idx_product_category_brand', table_name='products')
    op.drop_table('products')
    op.drop_index(op.f('ix_scraping_sites_id'), table_name='scraping_sites')
    op.drop_table('scraping_sites')
    # ### end Alembic commands ###
