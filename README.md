# E-commerce Web Scraping Application

A production-ready web scraping platform built on Scrapy with a modern React interface for managing and monitoring e-commerce data extraction across multiple websites.

## 🚀 Features

### Core Functionality
- **Multi-site Scraping**: Extensible framework supporting multiple e-commerce websites
- **JavaScript Support**: Playwright integration for dynamic content and SPA websites
- **Intelligent Data Extraction**: Automatic product detection with flexible schema
- **Data Deduplication**: Smart duplicate detection and price change tracking
- **Respectful Scraping**: Built-in rate limiting and robots.txt compliance

### Web Interface
- **Modern Dashboard**: Real-time statistics and system health monitoring
- **Site Management**: Easy configuration of scraping targets with validation
- **Product Browser**: Advanced filtering, search, and pagination
- **Job Monitoring**: Live job status tracking with detailed logs
- **Data Export**: CSV and JSON export with flexible filtering

### Technical Features
- **REST API**: Complete API for programmatic access and integration
- **Automated Scheduling**: Configurable daily scraping with Celery and Redis
- **Database Management**: PostgreSQL with automatic migrations and cleanup
- **Docker Deployment**: Complete containerized setup for easy deployment
- **Comprehensive Testing**: Full test suite for reliability

## 🎯 Target Websites

### Currently Supported
- **Coto Digital** (Argentina): Complete product catalog scraping
  - Products: names, prices, categories, descriptions, images, availability, SKUs
  - Features: pagination handling, category navigation, product detail extraction

### Easily Extensible
The framework is designed to easily add new e-commerce sites by creating new spiders following the established patterns.

## 🏗️ Architecture

```
├── ecommerce_scraper/     # Scrapy project with spiders and pipelines
│   ├── spiders/           # Website-specific scrapers
│   ├── items.py           # Data models for scraped items
│   ├── pipelines.py       # Data processing and validation
│   └── settings.py        # Scrapy configuration
├── api/                   # FastAPI backend
│   ├── routers/           # API endpoint definitions
│   ├── models.py          # Pydantic models for API
│   ├── tasks.py           # Celery background tasks
│   └── main.py            # FastAPI application
├── frontend/              # React TypeScript frontend
│   ├── src/components/    # Reusable UI components
│   ├── src/pages/         # Main application pages
│   ├── src/services/      # API communication layer
│   └── src/types/         # TypeScript type definitions
├── database/              # Database layer
│   ├── models.py          # SQLAlchemy ORM models
│   ├── crud.py            # Database operations
│   └── connection.py      # Database connection management
├── docker/                # Docker configuration
├── scripts/               # Utility and management scripts
├── tests/                 # Comprehensive test suite
└── alembic/               # Database migration management
```

## 🚀 Quick Start

### Option 1: Docker Deployment (Recommended)

1. **Clone and Configure**:
   ```bash
   git clone <repository>
   cd scrapy
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Deploy with Docker**:
   ```bash
   # Production deployment
   ./scripts/deploy.sh start

   # Development with hot reload
   ./scripts/deploy.sh dev
   ```

3. **Access the Application**:
   - **Web Interface**: http://localhost:3000 (dev) or http://localhost (prod)
   - **API Documentation**: http://localhost:8000/docs
   - **Health Check**: http://localhost:8000/health

### Option 2: Manual Development Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install
   cd frontend && npm install
   ```

2. **Database Setup**:
   ```bash
   python scripts/setup_database.py
   alembic upgrade head
   ```

3. **Start Services**:
   ```bash
   # Start all services with the management script
   python scripts/manage_services.py start

   # Or start individually:
   uvicorn api.main:app --reload                    # API Server
   celery -A api.celery_app worker --loglevel=info  # Background Worker
   celery -A api.celery_app beat --loglevel=info    # Scheduler
   cd frontend && npm start                         # Frontend (dev)
   ```

## 📚 Documentation

- **[Deployment Guide](DEPLOYMENT.md)**: Complete deployment instructions for production
- **[User Guide](USER_GUIDE.md)**: How to use the web interface and manage scraping
- **[API Documentation](http://localhost:8000/docs)**: Interactive API documentation (when running)

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/scrapy_db

# Job Queue
REDIS_URL=redis://localhost:6379/0

# Scraping Configuration
SCRAPING_SCHEDULE_HOUR=10          # Daily scraping time (24h format)
DOWNLOAD_DELAY=1                   # Delay between requests (seconds)
CONCURRENT_REQUESTS=16             # Max simultaneous requests
DATA_RETENTION_DAYS=90             # Data cleanup period

# Optional: Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
NOTIFICATION_EMAIL=<EMAIL>
```

## 🔌 API Endpoints

### Sites Management
- `GET /api/sites/` - List all scraping targets
- `POST /api/sites/` - Add new scraping target
- `PUT /api/sites/{id}` - Update scraping target
- `DELETE /api/sites/{id}` - Remove scraping target

### Products
- `GET /api/products/` - List products with filtering
- `GET /api/products/search?q=query` - Search products
- `GET /api/products/categories` - Get available categories
- `GET /api/products/{id}` - Get specific product

### Jobs
- `GET /api/jobs/` - List scraping jobs
- `POST /api/jobs/site/{site_id}` - Trigger manual scraping
- `GET /api/jobs/{id}` - Get job details
- `DELETE /api/jobs/{id}` - Cancel running job

### Data Export
- `GET /api/export/csv` - Export products as CSV
- `GET /api/export/json` - Export products as JSON

### Monitoring
- `GET /health` - System health check
- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/dashboard/metrics` - Detailed system metrics

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest

# Run specific test categories
pytest tests/test_api.py          # API endpoint tests
pytest tests/test_database.py     # Database and CRUD tests
pytest tests/test_spiders.py      # Spider functionality tests

# Run with coverage
pytest --cov=api --cov=database --cov=ecommerce_scraper
```

## 🚀 Production Deployment

### Docker Compose (Recommended)

```bash
# Production deployment with all services
docker-compose up -d

# Production with Nginx reverse proxy
docker-compose --profile production up -d
```

### Manual Production Setup

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed production deployment instructions including:
- Security considerations
- Performance optimization
- Monitoring and logging
- Backup and recovery
- Scaling strategies

## 🔒 Security Considerations

- **Authentication**: Add authentication layer for production use
- **Rate Limiting**: Respect target websites' terms of service
- **Data Privacy**: Comply with data protection regulations
- **Network Security**: Use firewalls and secure connections
- **Regular Updates**: Keep dependencies and Docker images updated

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow existing code style and patterns
- Add tests for new functionality
- Update documentation for user-facing changes
- Test with the provided Docker setup

## 📈 Monitoring and Maintenance

### Health Monitoring
- Built-in health checks at `/health`
- System metrics at `/api/dashboard/metrics`
- Real-time job monitoring in the web interface

### Automated Maintenance
- Automatic data cleanup based on retention settings
- Job queue monitoring and restart capabilities
- Database optimization and cleanup tasks

### Performance Optimization
- Configurable concurrency and delays
- Intelligent request scheduling
- Database indexing and query optimization

## 🆘 Support

### Troubleshooting
1. Check the [User Guide](USER_GUIDE.md) for common issues
2. Review application logs for error details
3. Use health check endpoints to diagnose problems
4. Check Docker container status and logs

### Getting Help
- Create an issue with detailed error information
- Include relevant log excerpts and configuration
- Specify your deployment method and environment

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Scrapy](https://scrapy.org/) for robust web scraping
- [Playwright](https://playwright.dev/) for JavaScript rendering
- [FastAPI](https://fastapi.tiangolo.com/) for modern API development
- [React](https://reactjs.org/) and [Material-UI](https://mui.com/) for the frontend
- [Celery](https://celeryproject.org/) for distributed task processing
