"""
Celery tasks for asynchronous processing.
"""
import os
import subprocess
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

from celery import current_task
from .celery_app import celery_app
from database.connection import get_db_session
from database.models import <PERSON>rapingSite, ScrapingJob, Product, JobStatus, SiteStatus
from database.crud import ScrapingJobCRUD, ScrapingSiteCRUD

logger = logging.getLogger(__name__)

# Path to the Scrapy project
SCRAPY_PROJECT_PATH = os.getenv('SCRAPY_PROJECT_PATH', '/app/ecommerce_scraper')


@celery_app.task(bind=True)
def run_spider(self, site_id: int, spider_name: str = None) -> Dict[str, Any]:
    """
    Run a Scrapy spider for a specific site.
    
    Args:
        site_id: ID of the site to scrape
        spider_name: Name of the spider to run (optional, will use site's spider_name)
    
    Returns:
        Dict with task results
    """
    task_id = self.request.id
    db_session = get_db_session()
    
    try:
        # Get site information
        site = ScrapingSiteCRUD.get_by_id(db_session, site_id)
        if not site:
            raise ValueError(f"Site with ID {site_id} not found")
        
        if not site.enabled:
            raise ValueError(f"Site {site.name} is disabled")
        
        spider_name = spider_name or site.spider_name
        
        # Create job record
        job = ScrapingJobCRUD.create(
            db_session,
            site_id=site_id,
            celery_task_id=task_id,
            status=JobStatus.RUNNING,
            started_at=datetime.utcnow()
        )
        
        logger.info(f"Starting spider {spider_name} for site {site.name} (Job ID: {job.id})")
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'job_id': job.id, 'site_name': site.name, 'status': 'starting'}
        )
        
        # Prepare Scrapy command
        scrapy_cmd = [
            'scrapy', 'crawl', spider_name,
            '-s', f'SITE_ID={site_id}',
            '-s', f'JOB_ID={job.id}',
            '-L', 'INFO'
        ]
        
        # Add custom settings from site config
        if site.config:
            for key, value in site.config.items():
                scrapy_cmd.extend(['-s', f'{key}={value}'])
        
        # Run Scrapy spider
        result = subprocess.run(
            scrapy_cmd,
            cwd=SCRAPY_PROJECT_PATH,
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout
        )
        
        # Parse Scrapy output for statistics
        items_scraped = 0
        items_saved = 0
        errors_count = 0
        
        if result.stdout:
            # Extract statistics from Scrapy output
            for line in result.stdout.split('\n'):
                if 'item_scraped_count' in line:
                    try:
                        items_scraped = int(line.split(':')[-1].strip())
                    except ValueError:
                        pass
                elif 'item_dropped_count' in line:
                    try:
                        dropped = int(line.split(':')[-1].strip())
                        items_saved = items_scraped - dropped
                    except ValueError:
                        pass
        
        # Determine job status
        job_status = JobStatus.COMPLETED if result.returncode == 0 else JobStatus.FAILED
        
        # Update job record
        job = ScrapingJobCRUD.update_status(
            db_session,
            job.id,
            job_status,
            completed_at=datetime.utcnow(),
            items_scraped=items_scraped,
            items_saved=items_saved,
            errors_count=errors_count,
            log_messages=result.stdout.split('\n') if result.stdout else [],
            error_messages=result.stderr.split('\n') if result.stderr else []
        )
        
        # Update site's last scraped time
        if job_status == JobStatus.COMPLETED:
            ScrapingSiteCRUD.update(db_session, site_id, last_scraped_at=datetime.utcnow())
        
        logger.info(f"Spider {spider_name} completed with status {job_status}")
        
        return {
            'job_id': job.id,
            'site_id': site_id,
            'site_name': site.name,
            'status': job_status,
            'items_scraped': items_scraped,
            'items_saved': items_saved,
            'errors_count': errors_count,
            'duration_seconds': job.duration_seconds
        }
        
    except subprocess.TimeoutExpired:
        # Handle timeout
        ScrapingJobCRUD.update_status(
            db_session,
            job.id,
            JobStatus.FAILED,
            completed_at=datetime.utcnow(),
            error_messages=['Task timed out after 1 hour']
        )
        logger.error(f"Spider {spider_name} timed out")
        raise
        
    except Exception as e:
        # Handle other errors
        if 'job' in locals():
            ScrapingJobCRUD.update_status(
                db_session,
                job.id,
                JobStatus.FAILED,
                completed_at=datetime.utcnow(),
                error_messages=[str(e)]
            )
        logger.error(f"Spider {spider_name} failed: {e}")
        raise
        
    finally:
        db_session.close()


@celery_app.task
def schedule_daily_scraping() -> Dict[str, Any]:
    """
    Schedule daily scraping for all active sites.
    
    Returns:
        Dict with scheduling results
    """
    db_session = get_db_session()
    scheduled_count = 0
    errors = []
    
    try:
        # Get all active sites
        active_sites = ScrapingSiteCRUD.get_active(db_session)
        
        for site in active_sites:
            try:
                # Check if site should be scraped today
                current_hour = datetime.now().hour
                current_minute = datetime.now().minute
                
                # Allow some flexibility in scheduling (±5 minutes)
                if (abs(current_hour - site.schedule_hour) == 0 and 
                    abs(current_minute - site.schedule_minute) <= 5):
                    
                    # Schedule the scraping task
                    run_spider.delay(site.id)
                    scheduled_count += 1
                    logger.info(f"Scheduled scraping for site: {site.name}")
                    
            except Exception as e:
                error_msg = f"Failed to schedule site {site.name}: {e}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        return {
            'scheduled_count': scheduled_count,
            'total_active_sites': len(active_sites),
            'errors': errors
        }
        
    except Exception as e:
        logger.error(f"Failed to schedule daily scraping: {e}")
        raise
    finally:
        db_session.close()


@celery_app.task
def cleanup_old_data() -> Dict[str, Any]:
    """
    Clean up old data based on retention settings.
    
    Returns:
        Dict with cleanup results
    """
    db_session = get_db_session()
    retention_days = int(os.getenv('DATA_RETENTION_DAYS', 90))
    cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
    
    try:
        # Count records to be deleted
        old_products = db_session.query(Product).filter(
            Product.last_updated_at < cutoff_date,
            Product.is_active == False
        ).count()
        
        old_jobs = db_session.query(ScrapingJob).filter(
            ScrapingJob.created_at < cutoff_date,
            ScrapingJob.status.in_([JobStatus.COMPLETED, JobStatus.FAILED])
        ).count()
        
        # Delete old inactive products
        deleted_products = db_session.query(Product).filter(
            Product.last_updated_at < cutoff_date,
            Product.is_active == False
        ).delete()
        
        # Delete old completed/failed jobs (keep recent ones for history)
        deleted_jobs = db_session.query(ScrapingJob).filter(
            ScrapingJob.created_at < cutoff_date,
            ScrapingJob.status.in_([JobStatus.COMPLETED, JobStatus.FAILED])
        ).delete()
        
        db_session.commit()
        
        logger.info(f"Cleanup completed: {deleted_products} products, {deleted_jobs} jobs deleted")
        
        return {
            'deleted_products': deleted_products,
            'deleted_jobs': deleted_jobs,
            'retention_days': retention_days,
            'cutoff_date': cutoff_date.isoformat()
        }
        
    except Exception as e:
        db_session.rollback()
        logger.error(f"Cleanup failed: {e}")
        raise
    finally:
        db_session.close()


@celery_app.task
def health_check() -> Dict[str, Any]:
    """
    Perform health check on the system.
    
    Returns:
        Dict with health status
    """
    db_session = get_db_session()
    
    try:
        # Check database connectivity
        db_session.execute("SELECT 1")
        db_healthy = True
    except Exception as e:
        db_healthy = False
        logger.error(f"Database health check failed: {e}")
    finally:
        db_session.close()
    
    # Check if Scrapy is available
    try:
        result = subprocess.run(['scrapy', 'version'], capture_output=True, timeout=10)
        scrapy_healthy = result.returncode == 0
    except Exception:
        scrapy_healthy = False
    
    return {
        'timestamp': datetime.utcnow().isoformat(),
        'database_healthy': db_healthy,
        'scrapy_healthy': scrapy_healthy,
        'overall_healthy': db_healthy and scrapy_healthy
    }
