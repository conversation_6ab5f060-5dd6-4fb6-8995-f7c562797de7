"""
FastAPI main application for the e-commerce scraping platform.
"""
import os
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from database.connection import db_manager, get_db
from .routers import sites, products, jobs, dashboard, export
from .celery_app import celery_app


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    try:
        # Create database tables if they don't exist
        db_manager.create_tables()
        print("Database tables created/verified")
        
        # Check database health
        if db_manager.health_check():
            print("Database connection healthy")
        else:
            print("Warning: Database connection issues detected")
            
    except Exception as e:
        print(f"Startup error: {e}")
    
    yield
    
    # Shutdown
    print("Application shutting down")


# Create FastAPI app
app = FastAPI(
    title="E-commerce Scraping API",
    description="REST API for managing e-commerce web scraping operations",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(sites.router, prefix="/api/sites", tags=["sites"])
app.include_router(products.router, prefix="/api/products", tags=["products"])
app.include_router(jobs.router, prefix="/api/jobs", tags=["jobs"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])
app.include_router(export.router, prefix="/api/export", tags=["export"])

# Mount static files for frontend
# frontend_build_path = os.path.join(os.path.dirname(__file__), "..", "frontend", "build")
# if os.path.exists(frontend_build_path):
#     app.mount("/static", StaticFiles(directory=os.path.join(frontend_build_path, "static")), name="static")
#     app.mount("/", StaticFiles(directory=frontend_build_path, html=True), name="frontend")


@app.get("/api")
async def api_root():
    """API root endpoint."""
    return {
        "message": "E-commerce Scraping API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    health_status = {
        "status": "healthy",
        "database": "unknown",
        "celery": "unknown"
    }
    
    # Check database
    try:
        if db_manager.health_check():
            health_status["database"] = "healthy"
        else:
            health_status["database"] = "unhealthy"
            health_status["status"] = "degraded"
    except Exception as e:
        health_status["database"] = f"error: {str(e)}"
        health_status["status"] = "degraded"
    
    # Check Celery
    try:
        # Simple ping to check if Celery is responsive
        result = celery_app.control.ping(timeout=1)
        if result:
            health_status["celery"] = "healthy"
        else:
            health_status["celery"] = "unhealthy"
            health_status["status"] = "degraded"
    except Exception as e:
        health_status["celery"] = f"error: {str(e)}"
        health_status["status"] = "degraded"
    
    status_code = 200 if health_status["status"] == "healthy" else 503
    return JSONResponse(content=health_status, status_code=status_code)


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler."""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc) if os.getenv("DEBUG", "false").lower() == "true" else "An error occurred"
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    debug = os.getenv("DEBUG", "false").lower() == "true"
    
    uvicorn.run(
        "api.main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
