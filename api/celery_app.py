"""
Celery application configuration for asynchronous task processing.
"""
import os
from celery import Celery
from celery.schedules import crontab

# Redis URL for broker and result backend
REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')

# Create Celery app
celery_app = Celery(
    'ecommerce_scraper',
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=['api.tasks']
)

# Celery configuration
celery_app.conf.update(
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='America/Argentina/Buenos_Aires',
    enable_utc=True,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        'master_name': 'mymaster',
        'visibility_timeout': 3600,
    },
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Routing
    task_routes={
        'api.tasks.run_spider': {'queue': 'scraping'},
        'api.tasks.cleanup_old_data': {'queue': 'maintenance'},
    },
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'daily-scraping': {
            'task': 'api.tasks.schedule_daily_scraping',
            'schedule': crontab(
                hour=int(os.getenv('SCRAPING_SCHEDULE_HOUR', 10)),
                minute=int(os.getenv('SCRAPING_SCHEDULE_MINUTE', 0))
            ),
        },
        'cleanup-old-data': {
            'task': 'api.tasks.cleanup_old_data',
            'schedule': crontab(hour=2, minute=0),  # Daily at 2 AM
        },
        'health-check': {
            'task': 'api.tasks.health_check',
            'schedule': 300.0,  # Every 5 minutes
        },
    },
)

# Optional: Configure logging
celery_app.conf.update(
    worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
)

if __name__ == '__main__':
    celery_app.start()
