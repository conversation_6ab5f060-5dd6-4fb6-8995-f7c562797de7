"""
API routes for managing scraping jobs.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import desc

from database.connection import get_db
from database.models import ScrapingJob, ScrapingSite
from database.crud import ScrapingJobCRUD, ScrapingSiteCRUD
from api.models import Job, JobCreate, JobWithSite, MessageResponse
from api.tasks import run_spider

router = APIRouter()


@router.get("/", response_model=List[JobWithSite])
async def list_jobs(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    site_id: Optional[int] = Query(None),
    db: Session = Depends(get_db)
):
    """List scraping jobs with pagination."""
    
    # Build query
    query = db.query(ScrapingJob, ScrapingSite.name.label('site_name')).join(
        ScrapingSite, ScrapingJob.site_id == ScrapingSite.id
    ).order_by(desc(ScrapingJob.created_at))
    
    # Apply site filter if provided
    if site_id:
        query = query.filter(ScrapingJob.site_id == site_id)
    
    # Apply pagination
    results = query.offset(skip).limit(limit).all()
    
    # Format response
    jobs = []
    for job, site_name in results:
        job_dict = {
            **job.__dict__,
            'site_name': site_name
        }
        jobs.append(JobWithSite(**job_dict))
    
    return jobs


@router.get("/recent", response_model=List[JobWithSite])
async def list_recent_jobs(
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """List recent scraping jobs."""
    jobs = ScrapingJobCRUD.get_recent(db, limit=limit)
    
    # Get site names
    result = []
    for job in jobs:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == job.site_id).first()
        job_dict = {
            **job.__dict__,
            'site_name': site.name if site else 'Unknown'
        }
        result.append(JobWithSite(**job_dict))
    
    return result


@router.get("/{job_id}", response_model=JobWithSite)
async def get_job(job_id: int, db: Session = Depends(get_db)):
    """Get a specific job by ID."""
    job = ScrapingJobCRUD.get_by_id(db, job_id)
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Job with ID {job_id} not found"
        )
    
    # Get site name
    site = db.query(ScrapingSite).filter(ScrapingSite.id == job.site_id).first()
    job_dict = {
        **job.__dict__,
        'site_name': site.name if site else 'Unknown'
    }
    
    return JobWithSite(**job_dict)


@router.post("/", response_model=Job, status_code=status.HTTP_201_CREATED)
async def create_job(job_data: JobCreate, db: Session = Depends(get_db)):
    """Create and start a new scraping job."""
    
    # Check if site exists and is active
    site = ScrapingSiteCRUD.get_by_id(db, job_data.site_id)
    if not site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {job_data.site_id} not found"
        )
    
    if not site.enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Site {site.name} is disabled"
        )
    
    try:
        # Create job record
        job = ScrapingJobCRUD.create(db, site_id=job_data.site_id)
        
        # Start the scraping task asynchronously
        task = run_spider.delay(job_data.site_id)
        
        # Update job with Celery task ID
        job = ScrapingJobCRUD.update_status(
            db, 
            job.id, 
            job.status,  # Keep current status
            celery_task_id=task.id
        )
        
        return job
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create job: {str(e)}"
        )


@router.post("/site/{site_id}", response_model=Job, status_code=status.HTTP_201_CREATED)
async def trigger_site_scraping(site_id: int, db: Session = Depends(get_db)):
    """Trigger scraping for a specific site."""
    
    # Check if site exists and is active
    site = ScrapingSiteCRUD.get_by_id(db, site_id)
    if not site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {site_id} not found"
        )
    
    if not site.enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Site {site.name} is disabled"
        )
    
    try:
        # Create job record
        job = ScrapingJobCRUD.create(db, site_id=site_id)
        
        # Start the scraping task asynchronously
        task = run_spider.delay(site_id)
        
        # Update job with Celery task ID
        job = ScrapingJobCRUD.update_status(
            db, 
            job.id, 
            job.status,  # Keep current status
            celery_task_id=task.id
        )
        
        return job
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to trigger scraping: {str(e)}"
        )


@router.get("/site/{site_id}", response_model=List[Job])
async def list_jobs_by_site(
    site_id: int,
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """List jobs for a specific site."""
    
    # Check if site exists
    site = ScrapingSiteCRUD.get_by_id(db, site_id)
    if not site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {site_id} not found"
        )
    
    jobs = ScrapingJobCRUD.get_by_site(db, site_id, limit=limit)
    return jobs


@router.delete("/{job_id}", response_model=MessageResponse)
async def cancel_job(job_id: int, db: Session = Depends(get_db)):
    """Cancel a running job."""
    
    job = ScrapingJobCRUD.get_by_id(db, job_id)
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Job with ID {job_id} not found"
        )
    
    # Only allow cancellation of pending or running jobs
    if job.status not in ["pending", "running"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel job with status: {job.status}"
        )
    
    try:
        # Cancel the Celery task if it exists
        if job.celery_task_id:
            from api.celery_app import celery_app
            celery_app.control.revoke(job.celery_task_id, terminate=True)
        
        # Update job status
        ScrapingJobCRUD.update_status(db, job_id, "cancelled")
        
        return MessageResponse(message=f"Job {job_id} cancelled successfully")
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel job: {str(e)}"
        )
