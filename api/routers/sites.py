"""
API routes for managing scraping sites.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from database.connection import get_db
from database.crud import ScrapingSiteCRUD
from api.models import Site, SiteCreate, SiteUpdate, MessageResponse

router = APIRouter()


@router.get("/", response_model=List[Site])
async def list_sites(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """List all scraping sites."""
    sites = ScrapingSiteCRUD.get_all(db, skip=skip, limit=limit)
    return sites


@router.get("/active", response_model=List[Site])
async def list_active_sites(db: Session = Depends(get_db)):
    """List all active scraping sites."""
    sites = ScrapingSiteCRUD.get_active(db)
    return sites


@router.get("/{site_id}", response_model=Site)
async def get_site(site_id: int, db: Session = Depends(get_db)):
    """Get a specific site by ID."""
    site = ScrapingSiteCRUD.get_by_id(db, site_id)
    if not site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {site_id} not found"
        )
    return site


@router.post("/", response_model=Site, status_code=status.HTTP_201_CREATED)
async def create_site(site_data: SiteCreate, db: Session = Depends(get_db)):
    """Create a new scraping site."""
    try:
        site = ScrapingSiteCRUD.create(
            db,
            name=site_data.name,
            base_url=str(site_data.base_url),
            spider_name=site_data.spider_name,
            enabled=site_data.enabled,
            schedule_hour=site_data.schedule_hour,
            schedule_minute=site_data.schedule_minute,
            config=site_data.config
        )
        return site
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create site: {str(e)}"
        )


@router.put("/{site_id}", response_model=Site)
async def update_site(
    site_id: int,
    site_data: SiteUpdate,
    db: Session = Depends(get_db)
):
    """Update a scraping site."""
    # Check if site exists
    existing_site = ScrapingSiteCRUD.get_by_id(db, site_id)
    if not existing_site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {site_id} not found"
        )
    
    # Prepare update data
    update_data = {}
    for field, value in site_data.dict(exclude_unset=True).items():
        if field == "base_url" and value:
            update_data[field] = str(value)
        else:
            update_data[field] = value
    
    try:
        updated_site = ScrapingSiteCRUD.update(db, site_id, **update_data)
        return updated_site
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update site: {str(e)}"
        )


@router.delete("/{site_id}", response_model=MessageResponse)
async def delete_site(site_id: int, db: Session = Depends(get_db)):
    """Delete a scraping site."""
    # Check if site exists
    existing_site = ScrapingSiteCRUD.get_by_id(db, site_id)
    if not existing_site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {site_id} not found"
        )
    
    try:
        success = ScrapingSiteCRUD.delete(db, site_id)
        if success:
            return MessageResponse(message=f"Site {existing_site.name} deleted successfully")
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete site"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete site: {str(e)}"
        )
