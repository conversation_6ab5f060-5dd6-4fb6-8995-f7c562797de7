"""
API routes for dashboard statistics and monitoring.
"""
from datetime import datetime, timedelta
from typing import Dict, Any, List
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from database.connection import get_db
from database.models import ScrapingSite, ScrapingJob, Product, SiteStatus, JobStatus
from database.crud import get_dashboard_stats
from api.models import DashboardStats, JobWithSite

router = APIRouter()


@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_statistics(db: Session = Depends(get_db)):
    """Get comprehensive dashboard statistics."""
    
    # Get basic stats
    stats = get_dashboard_stats(db)
    
    # Format recent jobs with site names
    recent_jobs_with_sites = []
    for job in stats['recent_jobs']:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == job.site_id).first()
        job_dict = {
            **job.__dict__,
            'site_name': site.name if site else 'Unknown'
        }
        recent_jobs_with_sites.append(JobWithSite(**job_dict))
    
    return DashboardStats(
        total_sites=stats['total_sites'],
        active_sites=stats['active_sites'],
        total_products=stats['total_products'],
        jobs_24h=stats['jobs_24h'],
        recent_jobs=recent_jobs_with_sites
    )


@router.get("/metrics")
async def get_detailed_metrics(db: Session = Depends(get_db)):
    """Get detailed metrics for monitoring."""
    
    # Time ranges
    now = datetime.utcnow()
    last_24h = now - timedelta(hours=24)
    last_7d = now - timedelta(days=7)
    last_30d = now - timedelta(days=30)
    
    # Job statistics
    jobs_24h = db.query(ScrapingJob).filter(ScrapingJob.created_at >= last_24h).count()
    jobs_7d = db.query(ScrapingJob).filter(ScrapingJob.created_at >= last_7d).count()
    jobs_30d = db.query(ScrapingJob).filter(ScrapingJob.created_at >= last_30d).count()
    
    # Job status breakdown (last 7 days)
    job_status_stats = db.query(
        ScrapingJob.status,
        func.count(ScrapingJob.id).label('count')
    ).filter(
        ScrapingJob.created_at >= last_7d
    ).group_by(ScrapingJob.status).all()
    
    job_status_breakdown = {status: count for status, count in job_status_stats}
    
    # Success rate (last 7 days)
    total_jobs_7d = sum(job_status_breakdown.values())
    successful_jobs_7d = job_status_breakdown.get(JobStatus.COMPLETED, 0)
    success_rate = (successful_jobs_7d / total_jobs_7d * 100) if total_jobs_7d > 0 else 0
    
    # Product statistics
    products_24h = db.query(Product).filter(
        Product.scraped_at >= last_24h,
        Product.is_active == True
    ).count()
    
    products_7d = db.query(Product).filter(
        Product.scraped_at >= last_7d,
        Product.is_active == True
    ).count()
    
    # Site statistics
    total_sites = db.query(ScrapingSite).count()
    active_sites = db.query(ScrapingSite).filter(ScrapingSite.status == SiteStatus.ACTIVE).count()
    enabled_sites = db.query(ScrapingSite).filter(ScrapingSite.enabled == True).count()
    
    # Average job duration (last 7 days, completed jobs only)
    avg_duration = db.query(
        func.avg(ScrapingJob.duration_seconds)
    ).filter(
        ScrapingJob.created_at >= last_7d,
        ScrapingJob.status == JobStatus.COMPLETED,
        ScrapingJob.duration_seconds.isnot(None)
    ).scalar()
    
    # Top performing sites (by products scraped in last 7 days)
    top_sites = db.query(
        ScrapingSite.name,
        func.count(Product.id).label('product_count')
    ).join(
        Product, ScrapingSite.id == Product.site_id
    ).filter(
        Product.scraped_at >= last_7d,
        Product.is_active == True
    ).group_by(
        ScrapingSite.id, ScrapingSite.name
    ).order_by(
        desc('product_count')
    ).limit(5).all()
    
    # Recent errors
    recent_errors = db.query(ScrapingJob).filter(
        ScrapingJob.status == JobStatus.FAILED,
        ScrapingJob.created_at >= last_24h
    ).order_by(desc(ScrapingJob.created_at)).limit(5).all()
    
    error_details = []
    for job in recent_errors:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == job.site_id).first()
        error_details.append({
            'job_id': job.id,
            'site_name': site.name if site else 'Unknown',
            'created_at': job.created_at,
            'error_messages': job.error_messages[:3] if job.error_messages else []  # First 3 errors
        })
    
    return {
        'timestamp': now.isoformat(),
        'jobs': {
            'last_24h': jobs_24h,
            'last_7d': jobs_7d,
            'last_30d': jobs_30d,
            'status_breakdown_7d': job_status_breakdown,
            'success_rate_7d': round(success_rate, 2),
            'avg_duration_seconds': round(avg_duration, 2) if avg_duration else None
        },
        'products': {
            'total_active': db.query(Product).filter(Product.is_active == True).count(),
            'scraped_24h': products_24h,
            'scraped_7d': products_7d
        },
        'sites': {
            'total': total_sites,
            'active': active_sites,
            'enabled': enabled_sites,
            'top_performers_7d': [{'name': name, 'products': count} for name, count in top_sites]
        },
        'errors': {
            'recent_failures': error_details
        }
    }


@router.get("/health")
async def get_system_health(db: Session = Depends(get_db)):
    """Get system health indicators."""
    
    now = datetime.utcnow()
    last_hour = now - timedelta(hours=1)
    
    # Check for recent activity
    recent_jobs = db.query(ScrapingJob).filter(ScrapingJob.created_at >= last_hour).count()
    recent_products = db.query(Product).filter(Product.scraped_at >= last_hour).count()
    
    # Check for stuck jobs (running for more than 2 hours)
    stuck_jobs = db.query(ScrapingJob).filter(
        ScrapingJob.status == JobStatus.RUNNING,
        ScrapingJob.started_at < now - timedelta(hours=2)
    ).count()
    
    # Check for sites that haven't been scraped recently
    stale_sites = db.query(ScrapingSite).filter(
        ScrapingSite.enabled == True,
        ScrapingSite.status == SiteStatus.ACTIVE,
        ScrapingSite.last_scraped_at < now - timedelta(days=2)
    ).count()
    
    # Overall health score
    health_issues = []
    if stuck_jobs > 0:
        health_issues.append(f"{stuck_jobs} stuck jobs detected")
    if stale_sites > 0:
        health_issues.append(f"{stale_sites} sites haven't been scraped in 2+ days")
    if recent_jobs == 0 and recent_products == 0:
        health_issues.append("No recent scraping activity")
    
    health_status = "healthy" if not health_issues else "warning" if len(health_issues) <= 2 else "critical"
    
    return {
        'status': health_status,
        'timestamp': now.isoformat(),
        'indicators': {
            'recent_jobs_1h': recent_jobs,
            'recent_products_1h': recent_products,
            'stuck_jobs': stuck_jobs,
            'stale_sites': stale_sites
        },
        'issues': health_issues
    }
