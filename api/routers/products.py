"""
API routes for managing scraped products.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from database.connection import get_db
from database.models import Product, ScrapingSite
from database.crud import ProductCRUD
from api.models import Product as ProductModel, ProductWithSite, PaginatedResponse

router = APIRouter()


@router.get("/", response_model=List[ProductWithSite])
async def list_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    site_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    brand: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    min_price: Optional[float] = Query(None, ge=0),
    max_price: Optional[float] = Query(None, ge=0),
    in_stock: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """List products with filtering and pagination."""
    
    # Build query
    query = db.query(Product, ScrapingSite.name.label('site_name')).join(
        ScrapingSite, Product.site_id == ScrapingSite.id
    ).filter(Product.is_active == True)
    
    # Apply filters
    if site_id:
        query = query.filter(Product.site_id == site_id)
    
    if category:
        query = query.filter(Product.category.ilike(f"%{category}%"))
    
    if brand:
        query = query.filter(Product.brand.ilike(f"%{brand}%"))
    
    if search:
        query = query.filter(
            or_(
                Product.name.ilike(f"%{search}%"),
                Product.description.ilike(f"%{search}%")
            )
        )
    
    if min_price is not None:
        query = query.filter(Product.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Product.price <= max_price)
    
    if in_stock is not None:
        query = query.filter(Product.in_stock == in_stock)
    
    # Order by last updated
    query = query.order_by(desc(Product.last_updated_at))
    
    # Apply pagination
    results = query.offset(skip).limit(limit).all()
    
    # Format response
    products = []
    for product, site_name in results:
        product_dict = {
            **product.__dict__,
            'site_name': site_name
        }
        products.append(ProductWithSite(**product_dict))
    
    return products


@router.get("/search", response_model=List[ProductWithSite])
async def search_products(
    q: str = Query(..., min_length=1),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    site_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Search products by name or description."""
    products = ProductCRUD.search(
        db, 
        query=q, 
        site_id=site_id, 
        category=category, 
        skip=skip, 
        limit=limit
    )
    
    # Get site names
    result = []
    for product in products:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == product.site_id).first()
        product_dict = {
            **product.__dict__,
            'site_name': site.name if site else 'Unknown'
        }
        result.append(ProductWithSite(**product_dict))
    
    return result


@router.get("/categories", response_model=List[str])
async def list_categories(
    site_id: Optional[int] = Query(None),
    db: Session = Depends(get_db)
):
    """List all unique product categories."""
    query = db.query(Product.category).filter(
        and_(
            Product.is_active == True,
            Product.category.isnot(None),
            Product.category != ''
        )
    )
    
    if site_id:
        query = query.filter(Product.site_id == site_id)
    
    categories = query.distinct().all()
    return [cat[0] for cat in categories if cat[0]]


@router.get("/brands", response_model=List[str])
async def list_brands(
    site_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """List all unique product brands."""
    query = db.query(Product.brand).filter(
        and_(
            Product.is_active == True,
            Product.brand.isnot(None),
            Product.brand != ''
        )
    )
    
    if site_id:
        query = query.filter(Product.site_id == site_id)
    
    if category:
        query = query.filter(Product.category.ilike(f"%{category}%"))
    
    brands = query.distinct().all()
    return [brand[0] for brand in brands if brand[0]]


@router.get("/{product_id}", response_model=ProductWithSite)
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """Get a specific product by ID."""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with ID {product_id} not found"
        )
    
    # Get site name
    site = db.query(ScrapingSite).filter(ScrapingSite.id == product.site_id).first()
    product_dict = {
        **product.__dict__,
        'site_name': site.name if site else 'Unknown'
    }
    
    return ProductWithSite(**product_dict)


@router.get("/site/{site_id}", response_model=List[ProductModel])
async def list_products_by_site(
    site_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """List products for a specific site."""
    # Check if site exists
    site = db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
    if not site:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Site with ID {site_id} not found"
        )
    
    products = ProductCRUD.get_by_site(db, site_id, skip=skip, limit=limit)
    return products


@router.get("/price-changes/{days}", response_model=List[ProductWithSite])
async def get_price_changes(
    days: int = Path(..., ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get products with recent price changes."""
    products = ProductCRUD.get_price_changes(db, days=days)
    
    # Get site names
    result = []
    for product in products:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == product.site_id).first()
        product_dict = {
            **product.__dict__,
            'site_name': site.name if site else 'Unknown'
        }
        result.append(ProductWithSite(**product_dict))
    
    return result


@router.delete("/{product_id}")
async def delete_product(product_id: int, db: Session = Depends(get_db)):
    """Mark a product as inactive (soft delete)."""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with ID {product_id} not found"
        )

    product.is_active = False
    db.commit()

    return {"message": f"Product {product.name} marked as inactive"}
