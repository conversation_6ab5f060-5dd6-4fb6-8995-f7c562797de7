"""
API routes for data export functionality.
"""
import csv
import json
from datetime import datetime
from io import String<PERSON>
from typing import Optional

from fastapi import APIRouter, Depends, Query, HTTPException, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from database.connection import get_db
from database.models import Product, ScrapingSite

router = APIRouter()


@router.get("/csv")
async def export_products_csv(
    site_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    date_from: Optional[datetime] = Query(None),
    date_to: Optional[datetime] = Query(None),
    include_inactive: bool = Query(False),
    db: Session = Depends(get_db)
):
    """Export products to CSV format."""
    
    # Build query
    query = db.query(Product, ScrapingSite.name.label('site_name')).join(
        ScrapingSite, Product.site_id == ScrapingSite.id
    )
    
    # Apply filters
    filters = []
    
    if not include_inactive:
        filters.append(Product.is_active == True)
    
    if site_id:
        filters.append(Product.site_id == site_id)
    
    if category:
        filters.append(Product.category.ilike(f"%{category}%"))
    
    if date_from:
        filters.append(Product.scraped_at >= date_from)
    
    if date_to:
        filters.append(Product.scraped_at <= date_to)
    
    if filters:
        query = query.filter(and_(*filters))
    
    # Order by most recent
    query = query.order_by(desc(Product.scraped_at))
    
    # Execute query
    results = query.all()
    
    if not results:
        raise HTTPException(status_code=404, detail="No products found matching the criteria")
    
    # Create CSV content
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    headers = [
        'ID', 'Site', 'Name', 'Category', 'Brand', 'Price', 'Original Price', 
        'Currency', 'In Stock', 'Stock Quantity', 'SKU', 'External ID', 'URL',
        'Description', 'Main Image URL', 'Rating', 'Review Count',
        'First Seen', 'Last Updated', 'Scraped At'
    ]
    writer.writerow(headers)
    
    # Write data rows
    for product, site_name in results:
        row = [
            product.id,
            site_name,
            product.name,
            product.category or '',
            product.brand or '',
            product.price or '',
            product.original_price or '',
            product.currency,
            'Yes' if product.in_stock else 'No' if product.in_stock is not None else '',
            product.stock_quantity or '',
            product.sku or '',
            product.external_id,
            product.url,
            (product.description or '')[:500],  # Truncate long descriptions
            product.main_image_url or '',
            product.rating or '',
            product.review_count or '',
            product.first_seen_at.isoformat() if product.first_seen_at else '',
            product.last_updated_at.isoformat() if product.last_updated_at else '',
            product.scraped_at.isoformat() if product.scraped_at else ''
        ]
        writer.writerow(row)
    
    # Prepare response
    output.seek(0)
    csv_content = output.getvalue()
    output.close()
    
    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"products_export_{timestamp}.csv"
    
    return StreamingResponse(
        iter([csv_content]),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/json")
async def export_products_json(
    site_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    date_from: Optional[datetime] = Query(None),
    date_to: Optional[datetime] = Query(None),
    include_inactive: bool = Query(False),
    limit: int = Query(1000, le=10000),
    db: Session = Depends(get_db)
):
    """Export products to JSON format."""
    
    # Build query
    query = db.query(Product, ScrapingSite.name.label('site_name')).join(
        ScrapingSite, Product.site_id == ScrapingSite.id
    )
    
    # Apply filters
    filters = []
    
    if not include_inactive:
        filters.append(Product.is_active == True)
    
    if site_id:
        filters.append(Product.site_id == site_id)
    
    if category:
        filters.append(Product.category.ilike(f"%{category}%"))
    
    if date_from:
        filters.append(Product.scraped_at >= date_from)
    
    if date_to:
        filters.append(Product.scraped_at <= date_to)
    
    if filters:
        query = query.filter(and_(*filters))
    
    # Order by most recent and apply limit
    query = query.order_by(desc(Product.scraped_at)).limit(limit)
    
    # Execute query
    results = query.all()
    
    if not results:
        raise HTTPException(status_code=404, detail="No products found matching the criteria")
    
    # Convert to JSON-serializable format
    products_data = []
    for product, site_name in results:
        product_dict = {
            'id': product.id,
            'site_name': site_name,
            'site_id': product.site_id,
            'name': product.name,
            'description': product.description,
            'category': product.category,
            'brand': product.brand,
            'price': product.price,
            'original_price': product.original_price,
            'currency': product.currency,
            'in_stock': product.in_stock,
            'stock_quantity': product.stock_quantity,
            'availability_text': product.availability_text,
            'sku': product.sku,
            'external_id': product.external_id,
            'url': product.url,
            'image_urls': product.image_urls,
            'main_image_url': product.main_image_url,
            'attributes': product.attributes,
            'rating': product.rating,
            'review_count': product.review_count,
            'first_seen_at': product.first_seen_at.isoformat() if product.first_seen_at else None,
            'last_updated_at': product.last_updated_at.isoformat() if product.last_updated_at else None,
            'scraped_at': product.scraped_at.isoformat() if product.scraped_at else None,
            'is_active': product.is_active
        }
        products_data.append(product_dict)
    
    # Create export metadata
    export_data = {
        'metadata': {
            'export_timestamp': datetime.now().isoformat(),
            'total_products': len(products_data),
            'filters': {
                'site_id': site_id,
                'category': category,
                'date_from': date_from.isoformat() if date_from else None,
                'date_to': date_to.isoformat() if date_to else None,
                'include_inactive': include_inactive,
                'limit': limit
            }
        },
        'products': products_data
    }
    
    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"products_export_{timestamp}.json"
    
    # Convert to JSON string
    json_content = json.dumps(export_data, indent=2, ensure_ascii=False)
    
    return StreamingResponse(
        iter([json_content]),
        media_type="application/json",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/sites/csv")
async def export_sites_csv(db: Session = Depends(get_db)):
    """Export sites configuration to CSV."""
    
    sites = db.query(ScrapingSite).order_by(ScrapingSite.name).all()
    
    if not sites:
        raise HTTPException(status_code=404, detail="No sites found")
    
    # Create CSV content
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    headers = [
        'ID', 'Name', 'Base URL', 'Spider Name', 'Status', 'Enabled',
        'Schedule Hour', 'Schedule Minute', 'Created At', 'Last Scraped At'
    ]
    writer.writerow(headers)
    
    # Write data rows
    for site in sites:
        row = [
            site.id,
            site.name,
            site.base_url,
            site.spider_name,
            site.status,
            'Yes' if site.enabled else 'No',
            site.schedule_hour,
            site.schedule_minute,
            site.created_at.isoformat() if site.created_at else '',
            site.last_scraped_at.isoformat() if site.last_scraped_at else ''
        ]
        writer.writerow(row)
    
    # Prepare response
    output.seek(0)
    csv_content = output.getvalue()
    output.close()
    
    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"sites_export_{timestamp}.csv"
    
    return StreamingResponse(
        iter([csv_content]),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
