"""
Pydantic models for API requests and responses.
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, HttpUrl, validator


class JobStatus(str, Enum):
    """Job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SiteStatus(str, Enum):
    """Site status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"


# Site models
class SiteBase(BaseModel):
    """Base site model."""
    name: str = Field(..., min_length=1, max_length=255)
    base_url: HttpUrl
    spider_name: str = Field(..., min_length=1, max_length=100)
    enabled: bool = True
    schedule_hour: int = Field(10, ge=0, le=23)
    schedule_minute: int = Field(0, ge=0, le=59)
    config: Dict[str, Any] = Field(default_factory=dict)


class SiteCreate(SiteBase):
    """Site creation model."""
    pass


class SiteUpdate(BaseModel):
    """Site update model."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    base_url: Optional[HttpUrl] = None
    spider_name: Optional[str] = Field(None, min_length=1, max_length=100)
    enabled: Optional[bool] = None
    schedule_hour: Optional[int] = Field(None, ge=0, le=23)
    schedule_minute: Optional[int] = Field(None, ge=0, le=59)
    config: Optional[Dict[str, Any]] = None
    status: Optional[SiteStatus] = None


class Site(SiteBase):
    """Site response model."""
    id: int
    status: SiteStatus
    created_at: datetime
    updated_at: datetime
    last_scraped_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Product models
class ProductBase(BaseModel):
    """Base product model."""
    name: str = Field(..., min_length=1, max_length=500)
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    price: Optional[float] = Field(None, ge=0)
    original_price: Optional[float] = Field(None, ge=0)
    currency: str = "ARS"
    in_stock: Optional[bool] = None
    stock_quantity: Optional[int] = Field(None, ge=0)
    availability_text: Optional[str] = None
    image_urls: List[str] = Field(default_factory=list)
    main_image_url: Optional[str] = None
    attributes: Dict[str, Any] = Field(default_factory=dict)
    rating: Optional[float] = Field(None, ge=0, le=5)
    review_count: Optional[int] = Field(None, ge=0)


class Product(ProductBase):
    """Product response model."""
    id: int
    site_id: int
    sku: Optional[str] = None
    external_id: str
    url: str
    first_seen_at: datetime
    last_updated_at: datetime
    scraped_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


class ProductWithSite(Product):
    """Product with site information."""
    site_name: str


# Job models
class JobBase(BaseModel):
    """Base job model."""
    site_id: int


class JobCreate(JobBase):
    """Job creation model."""
    pass


class Job(JobBase):
    """Job response model."""
    id: int
    status: JobStatus
    celery_task_id: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    items_scraped: int = 0
    items_saved: int = 0
    errors_count: int = 0
    log_messages: List[str] = Field(default_factory=list)
    error_messages: List[str] = Field(default_factory=list)
    job_metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class JobWithSite(Job):
    """Job with site information."""
    site_name: str


# Dashboard models
class DashboardStats(BaseModel):
    """Dashboard statistics model."""
    total_sites: int
    active_sites: int
    total_products: int
    jobs_24h: int
    recent_jobs: List[JobWithSite]


# Search and filter models
class ProductFilter(BaseModel):
    """Product filtering parameters."""
    site_id: Optional[int] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    min_price: Optional[float] = Field(None, ge=0)
    max_price: Optional[float] = Field(None, ge=0)
    in_stock: Optional[bool] = None
    search: Optional[str] = None


class PaginationParams(BaseModel):
    """Pagination parameters."""
    skip: int = Field(0, ge=0)
    limit: int = Field(100, ge=1, le=1000)


class PaginatedResponse(BaseModel):
    """Paginated response model."""
    items: List[Any]
    total: int
    skip: int
    limit: int
    has_next: bool
    has_prev: bool


# Export models
class ExportFormat(str, Enum):
    """Export format enumeration."""
    CSV = "csv"
    JSON = "json"


class ExportRequest(BaseModel):
    """Export request model."""
    format: ExportFormat = ExportFormat.CSV
    site_id: Optional[int] = None
    category: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    include_inactive: bool = False


# Response models
class MessageResponse(BaseModel):
    """Generic message response."""
    message: str


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    detail: Optional[str] = None


# Price history models
class PriceHistory(BaseModel):
    """Price history model."""
    id: int
    product_id: int
    price: float
    original_price: Optional[float] = None
    currency: str
    discount_percentage: Optional[float] = None
    in_stock: Optional[bool] = None
    stock_quantity: Optional[int] = None
    recorded_at: datetime
    
    class Config:
        from_attributes = True
