#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually run a spider for testing purposes.
"""
import os
import sys
import argparse
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from database.connection import db_manager
from database.crud import ScrapingSiteCRUD
from api.tasks import run_spider


def list_sites():
    """List all available sites."""
    print("Available sites:")
    print("-" * 50)
    
    try:
        db_session = db_manager.get_session()
        sites = ScrapingSiteCRUD.get_all(db_session)
        
        if not sites:
            print("No sites found. Run setup_database.py first.")
            return
        
        for site in sites:
            status_icon = "✓" if site.enabled and site.status == "active" else "✗"
            print(f"{status_icon} ID: {site.id:2d} | {site.name:20s} | {site.spider_name:15s} | {site.status}")
        
    except Exception as e:
        print(f"Error listing sites: {e}")
    finally:
        if 'db_session' in locals():
            db_session.close()


def run_spider_sync(site_id, spider_name=None):
    """Run a spider synchronously for testing."""
    print(f"Running spider for site ID: {site_id}")
    
    try:
        # Get site information
        db_session = db_manager.get_session()
        site = ScrapingSiteCRUD.get_by_id(db_session, site_id)
        
        if not site:
            print(f"Site with ID {site_id} not found")
            return False
        
        print(f"Site: {site.name}")
        print(f"Spider: {spider_name or site.spider_name}")
        print(f"URL: {site.base_url}")
        print("-" * 50)
        
        # Run the spider task
        result = run_spider(site_id, spider_name)
        
        print("Spider execution completed!")
        print(f"Status: {result['status']}")
        print(f"Items scraped: {result['items_scraped']}")
        print(f"Items saved: {result['items_saved']}")
        print(f"Errors: {result['errors_count']}")
        
        if result['duration_seconds']:
            print(f"Duration: {result['duration_seconds']:.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"Error running spider: {e}")
        return False
    finally:
        if 'db_session' in locals():
            db_session.close()


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run a spider manually")
    parser.add_argument("--list", action="store_true", help="List all available sites")
    parser.add_argument("--site-id", type=int, help="Site ID to scrape")
    parser.add_argument("--spider", help="Spider name (optional, uses site's default)")
    
    args = parser.parse_args()
    
    if args.list:
        list_sites()
        return 0
    
    if not args.site_id:
        print("Error: --site-id is required (use --list to see available sites)")
        return 1
    
    # Check database connectivity
    if not db_manager.health_check():
        print("Error: Cannot connect to database")
        return 1
    
    # Run the spider
    success = run_spider_sync(args.site_id, args.spider)
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
