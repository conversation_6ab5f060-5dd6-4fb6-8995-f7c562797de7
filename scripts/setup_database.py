#!/usr/bin/env python3
"""
Database setup script for the e-commerce scraping application.
"""
import os
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from database.connection import db_manager
from database.models import ScrapingSite, SiteStatus
from database.crud import ScrapingSiteCRUD


def create_tables():
    """Create all database tables."""
    print("Creating database tables...")
    try:
        db_manager.create_tables()
        print("✓ Database tables created successfully")
    except Exception as e:
        print(f"✗ Failed to create tables: {e}")
        return False
    return True


def create_initial_site():
    """Create the initial Coto Digital site."""
    print("Creating initial Coto Digital site...")
    
    try:
        db_session = db_manager.get_session()
        
        # Check if site already exists
        existing_site = db_session.query(ScrapingSite).filter(
            ScrapingSite.name == "Coto Digital"
        ).first()
        
        if existing_site:
            print("✓ Coto Digital site already exists")
            db_session.close()
            return True
        
        # Create the site
        site = ScrapingSiteCRUD.create(
            db_session,
            name="Coto Digital",
            base_url="https://www.cotodigital.com.ar/sitios/cdigi/nuevositio",
            spider_name="coto_digital",
            enabled=True,
            schedule_hour=10,
            schedule_minute=0,
            config={
                "download_delay": 2,
                "concurrent_requests": 8,
                "respect_robots_txt": True
            }
        )
        
        print(f"✓ Created Coto Digital site (ID: {site.id})")
        return True
        
    except Exception as e:
        print(f"✗ Failed to create initial site: {e}")
        return False
    finally:
        if 'db_session' in locals():
            db_session.close()


def check_database_health():
    """Check database connectivity and health."""
    print("Checking database health...")

    try:
        if db_manager.health_check():
            print("✓ Database connection is healthy")
            return True
        else:
            print("⚠ Database connection check returned false, but continuing...")
            return True  # Don't fail the setup for this
    except Exception as e:
        print(f"⚠ Database health check failed: {e}, but continuing...")
        return True  # Don't fail the setup for this


def main():
    """Main setup function."""
    print("=== E-commerce Scraper Database Setup ===\n")
    
    # Check environment variables
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("⚠ Warning: DATABASE_URL not set, using default")
    else:
        print(f"Using database: {database_url.split('@')[-1] if '@' in database_url else database_url}")
    
    print()
    
    # Run setup steps
    steps = [
        ("Create Tables", create_tables),
        ("Create Initial Site", create_initial_site),
        ("Database Health Check", check_database_health),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"Step: {step_name}")
        if step_func():
            success_count += 1
        print()
    
    # Summary
    print("=== Setup Summary ===")
    print(f"Completed: {success_count}/{len(steps)} steps")
    
    if success_count >= len(steps) - 1:  # Allow one step to fail (health check)
        print("✓ Database setup completed successfully!")
        print("\nNext steps:")
        print("1. Start the API server: uvicorn api.main:app --reload")
        print("2. Start Celery worker: celery -A api.celery_app worker --loglevel=info")
        print("3. Start Celery beat: celery -A api.celery_app beat --loglevel=info")
        return 0
    else:
        print("⚠ Setup completed with some warnings. Continuing anyway...")
        return 0  # Return success to allow container to continue


if __name__ == "__main__":
    exit(main())
