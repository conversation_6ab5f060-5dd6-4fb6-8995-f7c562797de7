#!/usr/bin/env python3
"""
Script to manage application services (<PERSON>, Celery worker, Celery beat).
"""
import os
import sys
import signal
import subprocess
import time
import argparse
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))


class ServiceManager:
    """Manages application services."""
    
    def __init__(self):
        self.processes = {}
        self.project_root = Path(__file__).parent.parent
    
    def start_api(self, host="0.0.0.0", port=8000, reload=True):
        """Start the FastAPI server."""
        print(f"Starting API server on {host}:{port}")
        
        cmd = [
            "uvicorn", "api.main:app",
            "--host", host,
            "--port", str(port)
        ]
        
        if reload:
            cmd.append("--reload")
        
        try:
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            self.processes['api'] = process
            print(f"✓ API server started (PID: {process.pid})")
            return True
        except Exception as e:
            print(f"✗ Failed to start API server: {e}")
            return False
    
    def start_worker(self, concurrency=4, loglevel="info"):
        """Start Celery worker."""
        print(f"Starting Celery worker (concurrency: {concurrency})")
        
        cmd = [
            "celery", "-A", "api.celery_app", "worker",
            "--loglevel", loglevel,
            "--concurrency", str(concurrency)
        ]
        
        try:
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            self.processes['worker'] = process
            print(f"✓ Celery worker started (PID: {process.pid})")
            return True
        except Exception as e:
            print(f"✗ Failed to start Celery worker: {e}")
            return False
    
    def start_beat(self, loglevel="info"):
        """Start Celery beat scheduler."""
        print("Starting Celery beat scheduler")
        
        cmd = [
            "celery", "-A", "api.celery_app", "beat",
            "--loglevel", loglevel
        ]
        
        try:
            process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            self.processes['beat'] = process
            print(f"✓ Celery beat started (PID: {process.pid})")
            return True
        except Exception as e:
            print(f"✗ Failed to start Celery beat: {e}")
            return False
    
    def stop_service(self, service_name):
        """Stop a specific service."""
        if service_name not in self.processes:
            print(f"Service '{service_name}' is not running")
            return False
        
        process = self.processes[service_name]
        if process.poll() is not None:
            print(f"Service '{service_name}' is already stopped")
            del self.processes[service_name]
            return True
        
        print(f"Stopping {service_name}...")
        try:
            process.terminate()
            process.wait(timeout=10)
            print(f"✓ {service_name} stopped")
        except subprocess.TimeoutExpired:
            print(f"Force killing {service_name}...")
            process.kill()
            process.wait()
            print(f"✓ {service_name} force stopped")
        
        del self.processes[service_name]
        return True
    
    def stop_all(self):
        """Stop all running services."""
        print("Stopping all services...")
        for service_name in list(self.processes.keys()):
            self.stop_service(service_name)
    
    def status(self):
        """Show status of all services."""
        print("Service Status:")
        print("-" * 40)
        
        services = ['api', 'worker', 'beat']
        for service in services:
            if service in self.processes:
                process = self.processes[service]
                if process.poll() is None:
                    print(f"✓ {service:10s} - Running (PID: {process.pid})")
                else:
                    print(f"✗ {service:10s} - Stopped")
                    del self.processes[service]
            else:
                print(f"✗ {service:10s} - Not started")
    
    def logs(self, service_name, lines=50):
        """Show logs for a service."""
        if service_name not in self.processes:
            print(f"Service '{service_name}' is not running")
            return
        
        process = self.processes[service_name]
        print(f"Logs for {service_name} (last {lines} lines):")
        print("-" * 50)
        
        # This is a simplified version - in production you'd want proper log management
        print("(Live logs - press Ctrl+C to stop)")
        try:
            for line in process.stdout:
                print(line.strip())
        except KeyboardInterrupt:
            print("\nStopped viewing logs")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Manage application services")
    parser.add_argument("action", choices=["start", "stop", "restart", "status", "logs"],
                       help="Action to perform")
    parser.add_argument("--service", choices=["api", "worker", "beat", "all"],
                       default="all", help="Service to manage")
    parser.add_argument("--host", default="0.0.0.0", help="API host (default: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="API port (default: 8000)")
    parser.add_argument("--concurrency", type=int, default=4, help="Worker concurrency (default: 4)")
    parser.add_argument("--no-reload", action="store_true", help="Disable API auto-reload")
    
    args = parser.parse_args()
    
    manager = ServiceManager()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\nShutting down services...")
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if args.action == "start":
        if args.service in ["api", "all"]:
            manager.start_api(args.host, args.port, not args.no_reload)
        
        if args.service in ["worker", "all"]:
            manager.start_worker(args.concurrency)
        
        if args.service in ["beat", "all"]:
            manager.start_beat()
        
        if args.service == "all":
            print("\n✓ All services started")
            print("Press Ctrl+C to stop all services")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                manager.stop_all()
    
    elif args.action == "stop":
        if args.service == "all":
            manager.stop_all()
        else:
            manager.stop_service(args.service)
    
    elif args.action == "restart":
        if args.service == "all":
            manager.stop_all()
            time.sleep(2)
            manager.start_api(args.host, args.port, not args.no_reload)
            manager.start_worker(args.concurrency)
            manager.start_beat()
        else:
            manager.stop_service(args.service)
            time.sleep(2)
            if args.service == "api":
                manager.start_api(args.host, args.port, not args.no_reload)
            elif args.service == "worker":
                manager.start_worker(args.concurrency)
            elif args.service == "beat":
                manager.start_beat()
    
    elif args.action == "status":
        manager.status()
    
    elif args.action == "logs":
        if args.service == "all":
            print("Please specify a specific service for logs")
        else:
            manager.logs(args.service)


if __name__ == "__main__":
    main()
