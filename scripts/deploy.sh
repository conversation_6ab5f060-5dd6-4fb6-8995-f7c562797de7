#!/bin/bash

# Deployment script for e-commerce scraper

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

echo -e "${GREEN}🚀 E-commerce Scraper Deployment Script${NC}"
echo "========================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ docker-compose is not installed. Please install it and try again.${NC}"
    exit 1
fi

# Check if .env file exists
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${YELLOW}⚠️  .env file not found. Creating from .env.example...${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ Created .env file. Please review and update it with your settings.${NC}"
    else
        echo -e "${RED}❌ .env.example file not found. Please create a .env file manually.${NC}"
        exit 1
    fi
fi

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  build     Build all images"
    echo "  logs      Show logs"
    echo "  status    Show service status"
    echo "  clean     Clean up containers and volumes"
    echo "  dev       Start in development mode"
    echo "  prod      Start in production mode with nginx"
    echo ""
}

# Function to start services
start_services() {
    echo -e "${GREEN}🔄 Starting services...${NC}"
    docker-compose -f $COMPOSE_FILE up -d
    echo -e "${GREEN}✅ Services started successfully!${NC}"
    echo ""
    echo "🌐 Application URLs:"
    echo "   API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo "   Health Check: http://localhost:8000/health"
    echo ""
    echo "📊 Monitoring:"
    echo "   PostgreSQL: localhost:5432"
    echo "   Redis: localhost:6379"
}

# Function to stop services
stop_services() {
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    docker-compose -f $COMPOSE_FILE down
    echo -e "${GREEN}✅ Services stopped successfully!${NC}"
}

# Function to restart services
restart_services() {
    echo -e "${YELLOW}🔄 Restarting services...${NC}"
    docker-compose -f $COMPOSE_FILE restart
    echo -e "${GREEN}✅ Services restarted successfully!${NC}"
}

# Function to build images
build_images() {
    echo -e "${GREEN}🔨 Building images...${NC}"
    docker-compose -f $COMPOSE_FILE build --no-cache
    echo -e "${GREEN}✅ Images built successfully!${NC}"
}

# Function to show logs
show_logs() {
    echo -e "${GREEN}📋 Showing logs...${NC}"
    docker-compose -f $COMPOSE_FILE logs -f
}

# Function to show status
show_status() {
    echo -e "${GREEN}📊 Service Status:${NC}"
    docker-compose -f $COMPOSE_FILE ps
    echo ""
    echo -e "${GREEN}🔍 Health Checks:${NC}"
    
    # Check API health
    if curl -s http://localhost:8000/health > /dev/null; then
        echo -e "   API: ${GREEN}✅ Healthy${NC}"
    else
        echo -e "   API: ${RED}❌ Unhealthy${NC}"
    fi
    
    # Check PostgreSQL
    if docker-compose -f $COMPOSE_FILE exec -T postgres pg_isready > /dev/null 2>&1; then
        echo -e "   PostgreSQL: ${GREEN}✅ Healthy${NC}"
    else
        echo -e "   PostgreSQL: ${RED}❌ Unhealthy${NC}"
    fi
    
    # Check Redis
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping > /dev/null 2>&1; then
        echo -e "   Redis: ${GREEN}✅ Healthy${NC}"
    else
        echo -e "   Redis: ${RED}❌ Unhealthy${NC}"
    fi
}

# Function to clean up
clean_up() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    read -p "This will remove all containers and volumes. Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f $COMPOSE_FILE down -v --remove-orphans
        docker system prune -f
        echo -e "${GREEN}✅ Cleanup completed!${NC}"
    else
        echo -e "${YELLOW}❌ Cleanup cancelled.${NC}"
    fi
}

# Function to start in development mode
start_dev() {
    echo -e "${GREEN}🔧 Starting in development mode...${NC}"
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    echo -e "${GREEN}✅ Development environment started!${NC}"
    echo ""
    echo "🌐 Development URLs:"
    echo "   Frontend: http://localhost:3000"
    echo "   API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
}

# Function to start in production mode
start_prod() {
    echo -e "${GREEN}🚀 Starting in production mode...${NC}"
    docker-compose -f $COMPOSE_FILE --profile production up -d
    echo -e "${GREEN}✅ Production environment started!${NC}"
    echo ""
    echo "🌐 Production URLs:"
    echo "   Application: http://localhost"
    echo "   API: http://localhost/api"
}

# Main script logic
case "${1:-}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    build)
        build_images
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    clean)
        clean_up
        ;;
    dev)
        start_dev
        ;;
    prod)
        start_prod
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
