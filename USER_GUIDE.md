# User Guide

This guide explains how to use the E-commerce Web Scraping Application.

## Getting Started

### Accessing the Application

Once deployed, access the web interface at:
- Development: http://localhost:3000
- Production: http://localhost (or your domain)

The application consists of several main sections:
- **Dashboard**: Overview of system status and recent activity
- **Sites**: Manage scraping targets
- **Products**: Browse and search scraped products
- **Jobs**: Monitor scraping jobs
- **Settings**: System configuration and health

## Dashboard

The dashboard provides an overview of your scraping system:

### Key Metrics
- **Total Sites**: Number of configured scraping targets
- **Active Sites**: Sites currently enabled for scraping
- **Total Products**: Number of products in the database
- **Jobs (24h)**: Scraping jobs executed in the last 24 hours

### Recent Jobs Table
Shows the latest scraping jobs with:
- Site name
- Job status (pending, running, completed, failed, cancelled)
- Items scraped and saved
- Error count
- Creation time

## Managing Sites

### Adding a New Site

1. Go to the **Sites** section
2. Click **Add Site**
3. Fill in the required information:
   - **Name**: Descriptive name for the site
   - **Base URL**: Starting URL for scraping
   - **Spider Name**: Technical name for the spider (e.g., "coto_digital")
   - **Schedule**: Hour and minute for daily scraping (24-hour format)
   - **Enabled**: Whether the site should be scraped automatically

4. Click **Create**

### Editing a Site

1. In the Sites table, click the edit icon (pencil) for the site
2. Modify the desired fields
3. Click **Update**

### Manual Scraping

To trigger immediate scraping of a site:
1. In the Sites table, click the play icon for the site
2. A new job will be created and executed

### Deleting a Site

1. Click the delete icon (trash) for the site
2. Confirm the deletion
3. **Note**: This will also delete all associated products and jobs

## Browsing Products

### Product List

The Products section shows all scraped products with:
- Product image and name
- Site source
- Category and brand
- Current price (with original price if discounted)
- Stock status
- Last update time

### Filtering Products

Use the filter panel to narrow down products:
- **Search**: Text search in product names and descriptions
- **Site**: Filter by specific scraping site
- **Category**: Filter by product category
- **Brand**: Filter by product brand
- **Stock Status**: Show only in-stock or out-of-stock items

### Exporting Data

Export product data in two formats:
- **CSV**: Spreadsheet-compatible format
- **JSON**: Structured data format for developers

Click the export buttons at the top of the Products page.

## Monitoring Jobs

### Job Status

Jobs can have the following statuses:
- **Pending**: Waiting to start
- **Running**: Currently executing
- **Completed**: Finished successfully
- **Failed**: Encountered errors
- **Cancelled**: Manually stopped

### Job Details

Click the view icon (eye) to see detailed information:
- Execution timeline
- Items scraped vs. saved
- Error messages
- Log output

### Cancelling Jobs

For running or pending jobs, click the stop icon to cancel execution.

## System Settings

### Health Monitoring

The Settings page shows:
- **System Health**: Overall status of database and job queue
- **System Metrics**: Performance statistics
- **Product Statistics**: Data volume metrics
- **Site Statistics**: Configuration summary

### Data Management

- **Export All Data**: Download complete product database
- **Export Sites Config**: Download site configuration

## Best Practices

### Site Configuration

1. **Respectful Scraping**:
   - Set appropriate delays between requests (1-2 seconds minimum)
   - Don't overload target servers
   - Respect robots.txt files

2. **Scheduling**:
   - Schedule scraping during off-peak hours
   - Stagger multiple sites to avoid conflicts
   - Consider target site's timezone

3. **Monitoring**:
   - Check job status regularly
   - Review error messages for failed jobs
   - Monitor data quality and completeness

### Data Management

1. **Regular Exports**:
   - Export data regularly for backup
   - Use CSV for analysis in spreadsheet tools
   - Use JSON for integration with other systems

2. **Data Cleanup**:
   - The system automatically removes old data
   - Adjust retention period in configuration if needed
   - Archive important data before it's cleaned up

### Troubleshooting

#### Common Issues

1. **Jobs Failing**:
   - Check if target website is accessible
   - Review error messages in job details
   - Verify site configuration is correct

2. **No Products Scraped**:
   - Website structure may have changed
   - Check if spider selectors are still valid
   - Review job logs for parsing errors

3. **Slow Performance**:
   - Reduce concurrent requests
   - Increase delays between requests
   - Check system resources

#### Getting Help

1. **Check Job Logs**: Most issues are explained in job error messages
2. **Review System Health**: Check if all services are running properly
3. **Monitor Resources**: Ensure adequate CPU, memory, and disk space

## API Usage

For developers, the application provides a REST API:

### API Documentation
- Interactive docs: http://localhost:8000/docs
- OpenAPI spec: http://localhost:8000/redoc

### Common Endpoints

```bash
# Get all sites
GET /api/sites/

# Create a new site
POST /api/sites/
{
  "name": "Example Site",
  "base_url": "https://example.com",
  "spider_name": "example_spider",
  "enabled": true,
  "schedule_hour": 10,
  "schedule_minute": 0
}

# Trigger manual scraping
POST /api/jobs/site/{site_id}

# Get products with filters
GET /api/products/?site_id=1&category=Electronics&in_stock=true

# Export data
GET /api/export/csv
GET /api/export/json
```

## Advanced Configuration

### Environment Variables

Key settings that can be adjusted:

- `DOWNLOAD_DELAY`: Delay between requests (seconds)
- `CONCURRENT_REQUESTS`: Number of simultaneous requests
- `DATA_RETENTION_DAYS`: How long to keep old data
- `SCRAPING_SCHEDULE_HOUR`: Default scraping time

### Custom Spiders

To add support for new websites:

1. Create a new spider in `ecommerce_scraper/spiders/`
2. Follow the pattern of existing spiders
3. Add the site through the web interface
4. Test with manual scraping

### Scaling

For high-volume scraping:

1. Increase Celery worker concurrency
2. Add more worker instances
3. Optimize database queries
4. Consider distributed deployment

## Security Considerations

1. **Access Control**: The application doesn't include authentication by default
2. **Network Security**: Use firewalls to restrict access
3. **Data Privacy**: Be aware of data protection regulations
4. **Rate Limiting**: Respect target websites' terms of service

## Support and Maintenance

### Regular Tasks

1. **Monitor job success rates**
2. **Review and update site configurations**
3. **Check system health regularly**
4. **Export important data for backup**
5. **Update the application periodically**

### Performance Optimization

1. **Database maintenance**: Regular VACUUM and ANALYZE
2. **Log rotation**: Prevent log files from growing too large
3. **Resource monitoring**: Watch CPU, memory, and disk usage
4. **Network optimization**: Ensure good connectivity to target sites
